import { NextRequest, NextResponse } from "next/server";
import { minioClient } from "@/lib/minio";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { prisma } from "@/src/lib/prisma";

// Tipos de callback do OnlyOffice
type OnlyOfficeCallbackStatus =
  | 0 // Documento não alterado
  | 1 // Documento sendo editado
  | 2 // Documento pronto para salvar
  | 3 // Erro ao salvar
  | 4 // Documento fechado com alterações
  | 6; // Fechamento com alterações não salvas

interface OnlyOfficeCallbackBody {
  key: string;
  status: OnlyOfficeCallbackStatus;
  url?: string;
  changesurl?: string;
  users?: string[];
  forcesavetype?: number;
}

function getStatusDescription(status: OnlyOfficeCallbackStatus): string {
  switch (status) {
    case 0:
      return "Documento não alterado";
    case 1:
      return "Documento sendo editado";
    case 2:
      return "Documento pronto para salvar";
    case 3:
      return "Erro ao salvar";
    case 4:
      return "Documento fechado com alterações";
    case 6:
      return "Fechamento com alterações não salvas";
    default:
      return `Status desconhecido: ${status}`;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as OnlyOfficeCallbackBody;
    const {
      key,
      status,
      url,
      changesurl: _changesurl,
      users,
      forcesavetype: _forcesavetype,
    } = body;

    // Marcar as variáveis como utilizadas para evitar o aviso do linter:
    void _changesurl;
    void _forcesavetype;

    console.log("=== CALLBACK ONLYOFFICE RECEBIDO ===");
    console.log("Timestamp:", new Date().toISOString());
    console.log("Body completo:", JSON.stringify(body, null, 2));
    console.log(
      "Status:",
      status,
      "- Descrição:",
      getStatusDescription(status)
    );
    console.log("Key:", key);
    console.log("URL:", url);
    console.log("Users:", users);
    console.log("=====================================");

    // Validação básica
    if (!key) {
      return NextResponse.json(
        { error: "Chave do documento não informada" },
        { status: 400 }
      );
    }

    const originalKey = key.split("_")[0]; // Extrai a chave original

    const fileRecord = await prisma.fileEditor.findUnique({
      where: { key: originalKey },
    });

    if (!fileRecord) {
      return NextResponse.json(
        { error: "Registro do arquivo não encontrado" },
        { status: 404 }
      );
    }

    switch (status) {
      case 1: // Documento sendo editado
        console.log(
          `Documento ${originalKey} está sendo editado por: ${users?.join(
            ", "
          )}`
        );
        await handleLock(originalKey, body.users);
        break;

      case 2: // Documento pronto para salvar (salvamento normal)
      case 6: // Fechamento com alterações não salvas (force save)
        await handleDocumentSave(originalKey, url, fileRecord);
        break;

      case 3: // Erro ao salvar
        console.error(`Erro ao salvar o documento ${originalKey}`);
      case 4: // Documento fechado com alterações
        console.log(`Documento ${originalKey} foi fechado com alterações`);
        await handleUnlock(originalKey);
        break;

      case 0: // Nenhuma alteração
      default:
        console.log(`Nenhuma alteração para o documento ${originalKey}`);
        break;
    }

    return NextResponse.json({ error: 0 }, { status: 200 });
  } catch (error) {
    console.error("Erro no processamento do callback:", error);
    return NextResponse.json(
      { error: "Error processing callback" },
      { status: 500 }
    );
  }
}

async function handleLock(originalKey: string, users?: string[]) {
  await prisma.fileEditor.update({
    where: { key: originalKey },
    data: {
      isLocked: true,
      lockedBy: users || [],
    },
  });
  console.log(`Documento bloqueado: ${originalKey} por ${users?.join(", ")}`);
}

async function handleUnlock(originalKey: string) {
  await prisma.fileEditor.update({
    where: { key: originalKey },
    data: {
      isLocked: false,
      lockedBy: [],
    },
  });
  console.log(`Documento desbloqueado: ${originalKey}`);
}

async function handleDocumentSave(
  originalKey: string,
  url: string | undefined,
  fileRecord: any
) {
  if (!url) {
    console.error(
      `ERRO: URL do documento editado não informada para key: ${originalKey}`
    );
    throw new Error("URL do documento editado não informada");
  }

  console.log(`=== INICIANDO SALVAMENTO ===`);
  console.log(`Key: ${originalKey}`);
  console.log(`URL: ${url}`);
  console.log(
    `FileRecord:`,
    fileRecord
      ? { id: fileRecord.id, bucket: fileRecord.bucket, key: fileRecord.key }
      : "null"
  );

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Falha ao baixar arquivo: ${response.statusText}`);
  }

  const fileBuffer = Buffer.from(await response.arrayBuffer());
  const newVersion = Date.now();

  // Mantém a mesma chave com metadado de versão
  await minioClient.send(
    new PutObjectCommand({
      Bucket: fileRecord.bucket,
      Key: fileRecord.key, // Mantém a key original
      Body: fileBuffer,
      ContentType: fileRecord.mimetype,
      Metadata: {
        version: newVersion.toString(), // Versão como metadado
      },
    })
  );

  // Atualiza apenas a versão e dados relacionados
  await prisma.fileEditor.update({
    where: { key: originalKey },
    data: {
      version: newVersion,
      size: fileBuffer.length,
      updatedAt: new Date(),
      isLocked: false,
      lockedBy: [],
    },
  });

  console.log(`=== SALVAMENTO CONCLUÍDO ===`);
  console.log(`Key: ${originalKey}`);
  console.log(`Nova versão: ${newVersion}`);
  console.log(`Tamanho do arquivo: ${fileBuffer.length} bytes`);
  console.log(`Timestamp: ${new Date().toISOString()}`);
  console.log(`=============================`);
}
