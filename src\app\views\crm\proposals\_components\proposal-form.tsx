"use client"

import { CustomInput } from "@/src/components/app-input";
import { TwoColumnRadioGroup } from "@/src/components/two-column-radio-group";
import { Button } from "@/src/components/ui/button";
import { CurrencyInput } from "@/src/components/currency-input";
import { UnsavedChangesDialog } from "@/src/components/unsaved-changes-dialog";
import { Label } from "@/src/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import {
  formatCurrency,
  parseCurrencyToNumber,
} from "@/src/lib/utils";


import { getProposalById, saveProposal } from "@/src/actions/proposals";

import { states } from "@/src/constants";
// import { useToast } from "@/src/hooks/use-toast"; // Não utilizado, usando useCenterToast
import { useCenterToast } from "@/src/hooks/use-center-toast";
import { toast } from "@/src/hooks/use-toast";
import { Periodicity } from "@/src/types/common";
import { Customer } from "@/src/types/core/customer";
// import { File } from "@/src/types/core/file"; // Comentado para evitar warnings
import {
  PlanningFrequencyItemInterface,
  Proposal,
} from "@/src/types/core/proposal";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { ServicesScope } from "@/src/types/core/services-scope";
import { zodResolver } from "@hookform/resolvers/zod";
import { Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState, useCallback } from "react";
import { SendProposalEmailWithAttachment } from "./send-proposal-email-with-attachment";
import { FormProvider, useForm, Controller } from "react-hook-form";
import { ProposalSchema, proposalsSchema } from "../_schemas/proposals.schema";
import { PlanningForms } from "./planning-form";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
} from "@/src/components/ui/alert-dialog";


interface ProposalFormProps {
  proposal?: Proposal;
  onCancelClick?: () => void;
  onChange?: () => void;
  onStatusChange?: (proposalId: string, newStatus: string) => Promise<void>;
  customers: Customer[];
  scopes: ServicesScope[];
  templates: ProposalTemplateInterface[];
  isContractValidated?: boolean;
  isEditing?: boolean;
  isLoading?: boolean;
  onSheetCloseAttempt?: (checkFn: () => void) => void;
}


export default function ProposalForm({
  proposal = undefined,
  customers = [],
  scopes = [],
  templates = [],
  onCancelClick,
  onChange,
  onStatusChange,
  isContractValidated,
  isEditing = false,
  isLoading = false,
  // onSheetCloseAttempt removido para evitar loops infinitos
}: ProposalFormProps) {
  // Log para depuração - desativado para análise de performance
  // console.log('ProposalForm recebeu:', {
  //   proposal,
  //   customersCount: customers?.length,
  //   scopesCount: scopes?.length,
  //   templatesCount: templates?.length,
  //   customers,
  //   scopes,
  //   templates
  // });

  // Hooks para toast
  const { success: showSuccess, error: showError } = useCenterToast();
  const [loading, setLoading] = useState(false);
  const [sendingToClient, setSendingToClient] = useState(false);
  const [planningNumber, setPlanningNumber] = useState<number>(proposal?.plannings?.length || 0);
  const [planningData, setPlanningsData] = useState<PlanningFrequencyItemInterface[]>(proposal?.plannings || []);
  // Estado para armazenar o arquivo da proposta - será utilizado em implementações futuras
  // Comentado para evitar warnings de variável não utilizada
  // const [file, setFile] = useState<File | undefined>(undefined);
  const [installments, setInstallments] = useState<
    { label: string; value: string }[]
  >([]);
  const [templatesFiltered, setTemplatesFiltered] = useState<ProposalTemplateInterface[]>([]);
  // Estado para controlar a exibição do botão de visualizar
  const [proposalState, setProposalState] = useState<Proposal | undefined>(proposal);

  // Estados para controlar alterações não salvas
  const [formModified, setFormModified] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: 'cancel' | 'view' | 'send' | 'resend';
    callback?: () => void;
  } | null>(null);

  // O callback de fechamento do Sheet será registrado após a inicialização do formulário

  // Estado específico para controlar se o botão de visualizar deve ser exibido
  const [fileEditorIdAvailable, setFileEditorIdAvailable] = useState<boolean>(!!proposal?.fileEditorId);

  // Estado para forçar re-renderização
  const [renderKey, setRenderKey] = useState<number>(0);

  // Estado para controlar se os dados foram carregados
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);

  // Estados para o Combobox de clientes
  const [customerOptions, setCustomerOptions] = useState<ComboboxOption[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);

  // Estado para controlar o dialog de aviso de sobrescrita do documento
  const [showOverwriteDialog, setShowOverwriteDialog] = useState(false);
  const [pendingSubmit, setPendingSubmit] = useState<null | { skipLoading: boolean; resolve?: (v: any) => void }>(null);

  // Estado para controlar o dialog de confirmação ao visualizar
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [pendingView, setPendingView] = useState<null | (() => void)>(null);

  // Função para carregar os clientes iniciais
  const fetchInitialCustomers = async () => {
    try {
      setLoadingCustomers(true);
      const response = await fetch("/api/customers/search");
      if (!response.ok) {
        throw new Error("Falha ao carregar clientes");
      }
      const data = await response.json();
      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        setCustomerOptions([]);
      } else {
        console.log('Clientes carregados:', {
          count: data.length,
          proposalCustomerId: proposal?.customerId,
          hasProposalCustomer: proposal?.customerId ? data.some(c => c.value === proposal.customerId) : false
        });
        setCustomerOptions(data);
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar a lista de clientes",
        variant: "destructive",
      });
      setCustomerOptions([]);
    } finally {
      setLoadingCustomers(false);
    }
  };

  // Função para buscar clientes com base no termo de pesquisa
  const searchCustomers = async (search: string): Promise<ComboboxOption[]> => {
    try {
      // Se a pesquisa estiver vazia, retornar todos os clientes
      if (!search.trim()) {
        const response = await fetch(`/api/customers/search`);
        if (!response.ok) {
          throw new Error("Falha ao buscar clientes");
        }
        const data = await response.json();
        return Array.isArray(data) ? data : [];
      }

      // Limpar e normalizar o termo de busca
      const cleanSearch = search.trim();

      const response = await fetch(`/api/customers/search?search=${encodeURIComponent(cleanSearch)}`);
      if (!response.ok) {
        throw new Error("Falha ao buscar clientes");
      }
      const data = await response.json();

      // Garantir que o resultado seja sempre um array
      if (!Array.isArray(data)) {
        console.warn("API retornou dados não esperados:", data);
        return [];
      }

      // Ordenar resultados por relevância (exato primeiro, depois parcial)
      const sortedResults = data.sort((a, b) => {
        const aLabel = a.label.toLowerCase();
        const bLabel = b.label.toLowerCase();
        const searchLower = cleanSearch.toLowerCase();

        // Priorizar correspondências exatas
        if (aLabel === searchLower && bLabel !== searchLower) return -1;
        if (bLabel === searchLower && aLabel !== searchLower) return 1;

        // Priorizar correspondências que começam com o termo
        if (aLabel.startsWith(searchLower) && !bLabel.startsWith(searchLower)) return -1;
        if (bLabel.startsWith(searchLower) && !aLabel.startsWith(searchLower)) return 1;

        // Ordenar alfabeticamente para o resto
        return aLabel.localeCompare(bLabel);
      });

      return sortedResults;
    } catch (error) {
      console.error("Erro ao buscar clientes:", error);
      toast({
        title: "Erro",
        description: "Não foi possível buscar clientes",
        variant: "destructive",
      });
      return [];
    }
  };

  // Função para recarregar a proposta e atualizar o estado
  const reloadProposal = async (proposalId: string) => {
    try {
      const updatedProposal = await getProposalById(proposalId);
      if (updatedProposal) {
        setProposalState(updatedProposal);
        setFileEditorIdAvailable(!!updatedProposal.fileEditorId);
        // Forçar re-renderização
        setRenderKey(prev => prev + 1);
        return updatedProposal;
      }
    } catch (error) {
      console.error("Erro ao recarregar proposta:", error);
    }
    return null;
  };

  // Efeito para atualizar o estado quando a prop proposal mudar
  useEffect(() => {
    if (proposal) {
      // Atualizar o estado com a proposta atual
      setProposalState(proposal);
      // Atualizar o estado que controla a visibilidade do botão Visualizar
      setFileEditorIdAvailable(!!proposal.fileEditorId);
      // Resetar o estado de modificação quando a proposta for carregada
      setFormModified(false);
    } else {
      // Se não houver proposta, limpar o estado e desabilitar o botão Visualizar
      setProposalState(undefined);
      setFileEditorIdAvailable(false);

      // Resetar o formulário para os valores padrão
      methods.reset({
        name: "",
        customerId: "",
        proposalTemplateId: "",
        startDate: "",
        endDate: "",
        budget: "0",
        serviceScopes: [],
        customService: "",
        periodicity: "NONE",
        paymentCondition: "CASH",
        area: "0",
        cep: "",
        address: "",
        city: "",
        state: "",
        serviceType: "GERENCIAMENTO", // Definir "Gerenciamento" como valor padrão
        installmentNumber: "1",
        installmentAmount: "",
        downPayment: "0",
        situation: "NEW", // Garantir valor padrão para situation
      }, {
        // Não marcar os campos como tocados ao resetar o formulário
        keepTouched: false,
        // Não acionar validações ao resetar o formulário
        keepErrors: false,
        // Não marcar o formulário como submetido ao resetar
        keepIsSubmitted: false,
        // Não marcar o formulário como sujo ao resetar
        keepDirty: false,
        // Não manter os valores padrão ao resetar
        keepDefaultValues: false
      });

      // Limpar outros estados relacionados
      setPlanningNumber(0);
      setPlanningsData([]);
      setInstallments([]);
    }
  }, [proposal]);

  // Carregar clientes ao montar o componente
  useEffect(() => {
    fetchInitialCustomers();
  }, []);






  const router = useRouter();

  // Adicione este estado para controlar se os inputs devem estar desabilitados
  const isFormDisabled = proposal?.situation === "LOST" || proposal?.situation === "PROJECT_FINISHED";

  // Inicializar o formulário
  const methods = useForm<ProposalSchema>({
    resolver: zodResolver(proposalsSchema),
    mode: "all", // Alterar para "all" para que a validação ocorra em todos os eventos (onChange, onBlur, onSubmit)
    defaultValues: async () => {
      let formData = {
        name: "",
        customerId: "",
        proposalTemplateId: "",
        startDate: "",
        endDate: "",
        budget: "",
        serviceScopes: [],
        customService: "",
        periodicity: "NONE",
        paymentCondition: "CASH",
        area: "",
        cep: "",
        address: "",
        city: "",
        state: "",
        serviceType: "GERENCIAMENTO", // Definir "Gerenciamento" como valor padrão
        installmentNumber: "1",
        installmentAmount: "",
        downPayment: "",
        situation: "NEW", // Garantir valor padrão para situation
      } as ProposalSchema;


      if (proposal) {
        console.log('Inicializando formulário com proposta:', {
          id: proposal.id,
          name: proposal.name,
          proposalTemplateId: proposal.proposalTemplateId,
          hasProposalTemplate: !!proposal.proposalTemplate,
          templatesCount: templates?.length,
          templatesFilteredCount: templatesFiltered?.length
        });

        formData = {
          ...proposal,
          customerId: proposal.customerId!,
          proposalTemplateId: proposal.proposalTemplateId!,
          budget: proposal.budget,
          downPayment: proposal.downPayment?.toString(),
          installmentNumber: proposal.installmentNumber?.toString() || "1",
          installmentAmount: proposal.installmentAmount?.toString(),
          serviceScopes: proposal.serviceScopes && Array.isArray(proposal.serviceScopes) ? proposal.serviceScopes.map((scope) => scope.id || '') : [],
          customService: proposal.customService || "",
          serviceType: proposal.serviceType || "",
        };

        console.log('Valores iniciais do formulário:', {
          customerId: formData.customerId,
          proposalTemplateId: formData.proposalTemplateId,
          templateExisteNosFiltrados: templatesFiltered?.some(t => t.id === proposal.proposalTemplateId),
          renderKey,
          formData: formData, // Mostrar todos os valores iniciais
          serviceType: formData.serviceType, // Destacar o tipo de serviço
          installmentNumber: {
            value: formData.installmentNumber,
            type: typeof formData.installmentNumber
          },
          installmentAmount: {
            value: formData.installmentAmount,
            type: typeof formData.installmentAmount
          }
        });

        setPlanningNumber(proposal.plannings && Array.isArray(proposal.plannings) ? proposal.plannings.length : 0);
        setPlanningsData(proposal.plannings && Array.isArray(proposal.plannings) ? proposal.plannings : []);
        // setFile(proposal.file);
      }


      return formData;
    },
  });

  const planningFormRef = useRef<{
    planningForms: PlanningFrequencyItemInterface[];
    validate: () => boolean;
    hasErrors: () => boolean;
  } | null>(null);


  // Observar alterações no formulário
  const formValues = methods.watch();

  // Efeito para definir o customerId quando os clientes forem carregados e houver uma proposta
  useEffect(() => {
    if (proposal?.customerId && customerOptions?.length > 0 && !methods.getValues('customerId')) {
      const customerExists = customerOptions.some(c => c.value === proposal.customerId);

      if (customerExists) {
        console.log('Definindo customerId após carregamento dos clientes:', {
          customerId: proposal.customerId,
          currentFormValue: methods.getValues('customerId')
        });
        methods.setValue('customerId', proposal.customerId, {
          shouldValidate: false,
          shouldDirty: false
        });
      } else {
        console.warn('Cliente da proposta não encontrado após carregamento:', {
          customerId: proposal.customerId,
          availableCustomers: customerOptions.map(c => ({ value: c.value, label: c.label }))
        });
      }
    }
  }, [proposal?.customerId, customerOptions, methods]);  // Usar customerOptions em vez de customers

  // NOTA: O callback de fechamento do Sheet foi removido para evitar loops infinitos
  // Em vez disso, usamos um botão de cancelamento explícito que verifica alterações não salvas

  // Efeito para resetar o estado de modificação quando o proposalState for atualizado
  useEffect(() => {
    setFormModified(false);
  }, [proposalState]);

  // Referências para o estado de modificação do formulário e controle de execução
  const formModifiedRef = useRef(formModified);
  const formModifiedExecutingRef = useRef(false);

  // Atualizar a referência quando o estado mudar
  useEffect(() => {
    formModifiedRef.current = formModified;
  }, [formModified]);

  // Detectar alterações no formulário - com proteção contra loops
  useEffect(() => {
    // Se já está executando, não executar novamente
    if (formModifiedExecutingRef.current) return;

    // Marcar como executando
    formModifiedExecutingRef.current = true;

    // Não marcar como modificado durante o carregamento inicial ou após salvar
    if (methods.formState.isDirty && !methods.formState.isSubmitSuccessful) {
      if (!formModifiedRef.current) {
        // Verificar se houve alterações reais comparando com os valores iniciais
        const currentValues = normalizeFieldValues(methods.getValues());
        const initialValues = normalizeFieldValues(methods.formState.defaultValues);

        // Apenas marcar como modificado se houver diferenças reais
        const hasRealChanges = JSON.stringify(currentValues) !== JSON.stringify(initialValues);

        if (hasRealChanges) {
          // Identificar quais campos foram alterados
          const changedFields: Record<string, { old: any, new: any }> = {};

          // Comparar cada campo para identificar as alterações
          Object.keys(currentValues).forEach(key => {
            const currentValue = currentValues[key];
            const initialValue = initialValues ? initialValues[key] : undefined;

            // Verificar se o valor mudou
            if (JSON.stringify(currentValue) !== JSON.stringify(initialValue)) {
              changedFields[key] = {
                old: initialValue,
                new: currentValue
              };
            }
          });

          // Registrar os campos alterados no console
          console.log('Campos alterados (efeito):', changedFields);

          // Log específico para o campo installment
          if (changedFields.installmentNumber || changedFields.installmentAmount) {
            console.log('Detalhes do campo installment:', {
              installmentNumber: {
                current: currentValues.installmentNumber,
                initial: initialValues ? initialValues.installmentNumber : undefined,
                currentType: typeof currentValues.installmentNumber,
                initialType: initialValues ? typeof initialValues.installmentNumber : undefined
              },
              installmentAmount: {
                current: currentValues.installmentAmount,
                initial: initialValues ? initialValues.installmentAmount : undefined,
                currentType: typeof currentValues.installmentAmount,
                initialType: initialValues ? typeof initialValues.installmentAmount : undefined
              }
            });
          }

          setFormModified(true);
        }
      }
    } else if (methods.formState.isSubmitSuccessful) {
      // Resetar o estado de modificação após salvar com sucesso
      if (formModifiedRef.current) {
        setFormModified(false);
      }
    }

    // Resetar o flag
    formModifiedExecutingRef.current = false;
  }, [methods.formState.isDirty, methods.formState.isSubmitSuccessful]);

  // Definir a função de envio do formulário
  const handleSubmit = async (skipLoading = false, skipToast = false, forceRegenerateFile = false) => {
    if (methods.formState.isDirty && !skipToast) {
      setShowOverwriteDialog(true);
      setPendingSubmit({ skipLoading });
      return null;
    }

    // Validação manual do campo proposalTemplateId
    if (!formValues.proposalTemplateId || !formValues.proposalTemplateId.trim().length) {
      if (!skipToast) {
        showError("Atenção", "Selecione um template antes de salvar.");
      }
      return null;
    }

    // Validação: entrada não pode ser maior ou igual ao orçamento quando for à prazo
    if (formValues.paymentCondition === "INSTALLMENTS") {
      const budgetValue = parseCurrencyToNumber(formValues.budget?.toString() || '0');
      const downPaymentValue = parseCurrencyToNumber(formValues.downPayment?.toString() || '0');
      if (downPaymentValue >= budgetValue) {
        if (!skipToast) {
          showError("Atenção", "O valor de entrada não pode ser maior ou igual ao valor do orçamento.");
        }
        return null;
      }
    }

    // console.log('Iniciando submissão do formulário');

    // Verificar o estado de submissão atual
    // console.log('Estado de submissão:', methods.formState.isSubmitted);

    // Acionar a validação de todos os campos
    await methods.trigger();
    // Resultado da validação removido para evitar warnings

    // Forçar o estado de submissão para true
    // Definir uma variável no localStorage para indicar que o formulário foi submetido
    // Isso é usado para mostrar os erros de validação mesmo quando o formulário não foi realmente submetido
    if (typeof window !== 'undefined') {
      localStorage.setItem('proposal_form_submitted', 'true');

      // Definir um timeout para remover a variável após 5 segundos
      setTimeout(() => {
        localStorage.removeItem('proposal_form_submitted');
      }, 5000);
    }

    // Marcar todos os campos obrigatórios como tocados para mostrar os erros de validação
    const requiredFields = ['name', 'customerId', 'proposalTemplateId', 'startDate', 'endDate', 'budget', 'area', 'serviceType', 'serviceScopes'];

    // Forçar a revalidação de todos os campos obrigatórios
    for (const key of requiredFields) {
      try {
        // Marcar o campo como tocado para mostrar a validação
        const currentValue = methods.getValues(key as any);

        // Verificar se o campo está vazio
        const isEmpty =
          currentValue === undefined ||
          currentValue === null ||
          currentValue === '' ||
          (Array.isArray(currentValue) && currentValue.length === 0);

        // Se o campo estiver vazio, forçar a validação
        if (isEmpty) {
          methods.setValue(key as any, currentValue, {
            shouldValidate: true,
            shouldTouch: true,
            shouldDirty: true
          });

          // Forçar a validação do campo
          await methods.trigger(key as any);

          // console.log(`Campo ${key} validado (vazio):`, {
          //   valor: currentValue,
          //   erro: methods.formState.errors[key],
          //   tocado: methods.formState.touchedFields[key]
          // });
        }
      } catch (error) {
        console.error(`Erro ao validar campo ${key}:`, error);
      }
    }

    const plannings = planningFormRef.current?.planningForms;
    const formValid = proposalsSchema.safeParse(formValues).success;

    // Validar os campos do cronograma se a periodicidade não for "NONE"
    let planningsValid = true;
    if (formValues.periodicity !== "NONE" && planningFormRef.current) {
      planningsValid = planningFormRef.current.validate();

      // Se o cronograma não for válido, mostrar mensagem de erro
      if (!planningsValid && !skipToast) {
        showError("Atenção", "Preencha todos os campos obrigatórios do cronograma.");
        return null;
      }
    }

    if (formValid && planningsValid) {
      const proposalData = {
        ...formValues,
        plannings: plannings && Array.isArray(plannings) && plannings.length > 0
          ? plannings.map((planning, index) => ({
            order: planning.order || 0,
            content: planning.content || '',
            label: `${index + 1}° ${formValues.periodicity == "MONTHLY" ? "mês" : "semana"}`,
          }))
          : [{ order: 0, label: "Sem cronograma", content: "Sem cronograma" }],
        workTotalCost: 0, // Deixar workTotalCost zerado
      };
      const savedData = await saveData(proposalData, skipLoading, skipToast, forceRegenerateFile);

      // Verificar se o arquivo foi gerado e atualizar o estado imediatamente
      if (savedData && savedData.fileEditorId) {
        setProposalState(prev => ({
          ...prev,
          ...savedData,
          fileEditorId: savedData.fileEditorId
        }));
      }

      return savedData;
    } else if (!formValid && !skipToast) {
      // Coletar os erros do formulário
      const errors = methods.formState.errors;

      // Mapear os nomes dos campos para nomes mais amigáveis
      const fieldNames: Record<string, string> = {
        name: "Tipo da proposta",
        customerId: "Cliente",
        proposalTemplateId: "Template",
        startDate: "Data de início",
        endDate: "Data de fim",
        budget: "Orçamento",
        area: "Área",
        serviceScopes: "Escopo de serviços",
        serviceType: "Tipo de serviço",
        installmentNumber: "Número de parcelas"
      };

      // Criar uma lista de campos com erro
      const errorFields = Object.keys(errors)
        .map(field => fieldNames[field] || field)
        .join(", ");

      // Mostrar mensagem de erro com os campos específicos
      if (errorFields) {
        showError("Atenção", `Preencha os seguintes campos obrigatórios: ${errorFields}`);
      } else {
        showError("Atenção", "Preencha todos os campos obrigatórios do formulário.");
      }
    }

    return null;
  };
  const saveData = async (proposalData: ProposalSchema, skipLoading = false, skipToast = false, forceRegenerateFile = false) => {
    try {
      if (!skipLoading) {
        setLoading(true);
      }

      console.log("Iniciando salvamento da proposta:", {
        id: proposalData.id,
        isContractValidated: isContractValidated,
        situation: proposalData.situation,
        proposalTemplateId: proposalData.proposalTemplateId
      });

      // Garantir que situation nunca fique vazio
      if (!proposalData.situation || proposalData.situation === "") {
        proposalData.situation = "NEW";
      }

      const data = await saveProposal(proposalData, forceRegenerateFile);

      // Log para debug - verificar o que foi retornado pela API
      console.log("Resposta do saveProposal:", {
        success: !!data,
        id: data?.id,
        hasFileEditorId: !!data?.fileEditorId,
        situation: data?.situation
      });

      if (!data) {
        console.error("Erro: saveProposal retornou null ou undefined");
        if (!skipToast) {
          showError("Erro", "Não foi possível salvar a proposta. Tente novamente.");
        }
        return null;
      }

      // Garantir que o ID existe
      if (!data.id) {
        console.error("Erro: saveProposal retornou dados sem ID");
        if (!skipToast) {
          showError("Erro", "Dados da proposta incompletos. Tente novamente.");
        }
        return null;
      }

      if (!skipToast) {
        showSuccess("Sucesso", proposalData.id ? "Proposta atualizada com sucesso!" : "Proposta criada com sucesso!");
      }

      // Definir o ID no formulário
      methods.setValue("id", data.id);

      // Atualizar o estado da proposta com os novos dados
      // Atualizar o estado proposalState para controlar a exibição do botão de visualizar
      setProposalState(prev => ({
        ...prev,
        ...data,
        fileEditorId: data.fileEditorId || prev?.fileEditorId
      }));

      if (!data.fileEditorId) {
        // Se não tiver fileEditorId, pode ser que o processo de geração do arquivo ainda não terminou
        console.log("Arquivo da proposta não encontrado imediatamente. Tentando novamente em 2 segundos...");
        if (!skipToast) {
          showSuccess("Atenção", "O arquivo da proposta está sendo gerado. Aguarde um momento antes de visualizá-lo.");
        }

        // Tentar buscar a proposta novamente após 2 segundos para verificar se o arquivo foi gerado
        setTimeout(async () => {
          try {
            console.log(`Buscando proposta atualizada (ID: ${data.id})...`);
            const updatedProposal = await getProposalById(data.id);

            console.log("Proposta atualizada:", {
              found: !!updatedProposal,
              hasFileEditorId: !!updatedProposal?.fileEditorId
            });

            if (updatedProposal && updatedProposal.fileEditorId) {
              // Arquivo encontrado, atualizar o estado usando a função reloadProposal
              await reloadProposal(data.id);

              // Atualizar o estado da proposta original
              if (onChange) {
                onChange();
              }

              // Mostrar mensagem de sucesso
              if (!skipToast) {
                showSuccess("Sucesso", "Arquivo da proposta encontrado!");
              }

              // Forçar re-renderização
              setRenderKey(prev => prev + 1);
            } else {
              console.log("Arquivo ainda não encontrado após 2 segundos");
            }
          } catch (error) {
            console.error("Erro ao buscar proposta atualizada:", error);
          }
        }, 2000);
      } else {
        // Se já tiver fileEditorId, verificar se o arquivo existe
        try {
          console.log(`Verificando arquivo da proposta (ID: ${data.id}, FileEditorId: ${data.fileEditorId})...`);

          // Buscar a proposta atualizada para garantir que temos os dados mais recentes
          const updatedProposal = await getProposalById(data.id);

          console.log("Proposta atualizada:", {
            found: !!updatedProposal,
            hasFileEditorId: !!updatedProposal?.fileEditorId
          });

          if (updatedProposal) {
            const fileId = updatedProposal.fileEditorId || data.fileEditorId;

            // Atualizar o estado usando a função reloadProposal se houver fileId
            if (fileId) {
              await reloadProposal(data.id);
            } else {
              // Se não houver fileId, atualizar apenas o estado
              setProposalState({
                ...updatedProposal,
                fileEditorId: fileId
              });
            }

            // Atualizar o estado da proposta original
            if (onChange) {
              onChange();
            }

            // Mostrar mensagem de sucesso
            if (fileId && !skipToast) {
              showSuccess("Sucesso", "Arquivo da proposta encontrado!");

              // Forçar re-renderização
              setRenderKey(prev => prev + 1);
            }
          }
        } catch (error) {
          console.error("Erro ao verificar arquivo da proposta:", error);
        }
      }

      // Atualize os estados com os dados salvos
      if (data.plannings && Array.isArray(data.plannings)) {
        setPlanningNumber(data.plannings.length);
        setPlanningsData(data.plannings);
      }

      if (onChange) {
        onChange();
      }

      // Retornar os dados salvos para uso no handleSubmit
      return data;
    } catch (error) {
      console.error("Erro ao salvar proposta:", error);
      if (!skipToast) {
        showError("Erro", "Erro ao salvar proposta");
      }
      return null;
    } finally {
      if (!skipLoading) {
        setLoading(false);
      }
    }
  };

  // Efeito para verificar se todos os dados necessários foram carregados
  useEffect(() => {
    // Verificar se os dados necessários foram carregados
    const templatesLoaded = templates && templates.length > 0;
    const scopesLoaded = scopes && scopes.length > 0;
    const customersLoaded = customers && customers.length > 0;

    // Se estiver editando, verificar se a proposta foi carregada
    const proposalLoaded = !isEditing || (isEditing && !!proposal);

    // Definir o estado como carregado apenas quando todos os dados necessários estiverem disponíveis
    const allDataLoaded = templatesLoaded && scopesLoaded && customersLoaded && proposalLoaded;
    setDataLoaded(allDataLoaded);

    // console.log('Estado de carregamento dos dados:', {
    //   templatesLoaded,
    //   scopesLoaded,
    //   customersLoaded,
    //   proposalLoaded,
    //   dataLoaded: allDataLoaded,
    //   templatesCount: templates?.length,
    //   proposalTemplateId: proposal?.proposalTemplateId,
    //   templatesFiltered: templatesFiltered?.length
    // });

    // Quando todos os dados estiverem carregados e estivermos editando uma proposta existente
    if (allDataLoaded && isEditing && proposal?.proposalTemplateId && templatesFiltered?.length > 0) {
      // Verificar se o template da proposta está entre os templates filtrados
      const templateExists = templatesFiltered.some(t => t.id === proposal.proposalTemplateId);

      if (templateExists) {
        // Definir o valor do template no formulário
        setTimeout(() => {
          methods.setValue("proposalTemplateId", proposal.proposalTemplateId || "", {
            shouldValidate: false,
            shouldDirty: false
          });
        }, 100);
      }
    }
  }, [templates, scopes, customers, proposal, isEditing, templatesFiltered]);

  // Efeito separado para atualizar o valor do template quando os dados estiverem carregados
  // foi movido para o useEffect anterior para evitar duplicação

  // Efeito para limpar o template selecionado quando o tipo de serviço mudar
  useEffect(() => {
    // Não executar durante a inicialização ou se não houver templates filtrados
    if (!dataLoaded || templatesFiltered.length === 0) {
      return;
    }

    // Não limpar o template se estivermos editando uma proposta existente
    if (isEditing && proposal?.proposalTemplateId) {
      // Verificar se o template atual ainda está nos templates filtrados
      const templateExists = templatesFiltered.some(t => t.id === proposal.proposalTemplateId);
      // Se o template não existir mais nos filtrados, não fazer nada
      // O usuário precisará selecionar um novo template manualmente
      if (!templateExists) {
        console.log('Template atual não está mais disponível após mudança de tipo de serviço');
      }
      return;
    }

    // Só limpa se o template atual não for de contrato
    const currentTemplateId = methods.getValues("proposalTemplateId");
    const isCurrentTemplateContract = templatesFiltered.some(t => t.id === currentTemplateId && t.type === "CONTRACT");

    if (currentTemplateId && !isCurrentTemplateContract) {
      console.log('[DEBUG] Limpando proposalTemplateId por mudança de tipo de serviço:', { currentTemplateId, isCurrentTemplateContract });
      methods.setValue("proposalTemplateId", "", {
        shouldValidate: false,
        shouldDirty: true
      });
    } else {
      console.log('[DEBUG] Não limpou proposalTemplateId por mudança de tipo de serviço:', { currentTemplateId, isCurrentTemplateContract });
    }
  }, [formValues.serviceType, templatesFiltered, dataLoaded, isEditing, proposal?.proposalTemplateId, methods]);

  // Determinar se deve mostrar templates de contrato com base na situação da proposta
  const shouldShowContractTemplates = () => {
    if (!proposal) return false;

    // Situações que usam templates de contrato
    const contractSituations = [
      "PROPOSAL_ACCEPTED", // Adicionado para mostrar apenas templates de contrato na coluna "Proposta aceita"
      "SIGN_REQUESTED",
      "SIGNED",
      "PROJECT_IN_PROGRESS",
      "PROJECT_FINISHED"
    ];

    return contractSituations.includes(proposal.situation);
  };

  // Efeito para filtrar templates com base na situação da proposta - removido para evitar loops
  // Este efeito foi combinado com o efeito de inicialização abaixo

  // Referência para os templates filtrados
  const templatesFilteredRef = useRef(templatesFiltered);

  // Atualizar a referência quando os dados mudarem
  useEffect(() => {
    templatesFilteredRef.current = templatesFiltered;
  }, [templatesFiltered]);

  // Flag para controlar a execução do efeito de templates
  const templatesEffectExecutionFlag = useRef(false);

  // Inicialização e filtragem dos templates com suporte a edição
  useEffect(() => {
    // Se o efeito já foi executado neste ciclo, não executar novamente
    if (templatesEffectExecutionFlag.current) {
      return;
    }

    // Marcar que o efeito está sendo executado
    templatesEffectExecutionFlag.current = true;

    // Verificar se temos os dados necessários
    if (!templates || !Array.isArray(templates) || templates.length === 0) {
      if (templatesFiltered.length > 0) {
        setTemplatesFiltered([]);
      }
      templatesEffectExecutionFlag.current = false;
      return;
    }

    // Determinar se deve mostrar templates de contrato
    const contractSituations = [
      "PROPOSAL_ACCEPTED",
      "SIGN_REQUESTED",
      "SIGNED",
      "PROJECT_IN_PROGRESS",
      "PROJECT_FINISHED"
    ];

    const useContractTemplates = isContractValidated ||
      (proposal?.situation && contractSituations.includes(proposal.situation));

    console.log("Filtrando templates:", {
      useContractTemplates,
      isContractValidated,
      situation: proposal?.situation,
      totalTemplates: templates.length,
      contractTemplatesCount: templates.filter(t => t.type === "CONTRACT").length,
      proposalTemplatesCount: templates.filter(t => t.type !== "CONTRACT").length
    });

    // Encontrar o template atual da proposta (se estiver editando)
    const currentTemplate = isEditing && proposal?.proposalTemplateId ?
      templates.find(t => t.id === proposal.proposalTemplateId) : undefined;

    if (currentTemplate) {
      console.log("Template atual:", {
        id: currentTemplate.id,
        title: currentTemplate.title,
        type: currentTemplate.type
      });
    }

    // Obter o tipo de serviço atual
    const currentServiceType = formValues.serviceType || proposal?.serviceType || "GERENCIAMENTO";

    // Mapear o tipo de serviço para o tipo de template correspondente
    const getTemplateTypeForServiceType = (serviceType: string) => {
      const serviceTypeToTemplateType: Record<string, string> = {
        "INSPECAO": "INSPECTION",
        "FISCALIZACAO": "SUPERVISION",
        "GERENCIAMENTO": "PROPOSAL",
        "CONSULTORIA": "PROPOSAL",
        "PROJETO": "PROJECT"
      };

      return serviceTypeToTemplateType[serviceType] || "PROPOSAL";
    };

    // Filtrar templates pelo tipo apropriado (PROPOSAL ou CONTRACT) e pelo tipo de serviço
    const filteredTemplates = templates.filter(item => {
      // Primeiro filtrar pelo tipo principal (PROPOSAL ou CONTRACT)
      if (useContractTemplates) {
        // Para contratos, mostrar APENAS templates do tipo CONTRACT
        return item.type === "CONTRACT";
      } else {
        // Para propostas, filtrar pelo tipo de serviço
        const serviceTemplateType = getTemplateTypeForServiceType(currentServiceType);
        return item.type === serviceTemplateType || item.type === "PROPOSAL";
      }
    });

    console.log("Templates filtrados:", {
      count: filteredTemplates.length,
      contractCount: filteredTemplates.filter(t => t.type === "CONTRACT").length,
      proposalCount: filteredTemplates.filter(t => t.type !== "CONTRACT").length,
      templates: filteredTemplates.map(t => ({ id: t.id, title: t.title, type: t.type }))
    });

    // Atualizar o estado apenas se for diferente do atual
    const currentTemplatesStr = JSON.stringify(templatesFilteredRef.current);
    const newTemplatesStr = JSON.stringify(filteredTemplates);

    if (currentTemplatesStr !== newTemplatesStr) {
      setTemplatesFiltered(filteredTemplates);
    }

    // Resetar o flag para permitir execuções futuras
    templatesEffectExecutionFlag.current = false;
  }, [templates, isEditing, proposal?.proposalTemplateId, proposal?.situation, isContractValidated, formValues.serviceType, proposal?.serviceType]);


  // Função para buscar endereço por CEP
  const searchAddresByCep = async () => {
    const cep = formValues.cep?.replace("-", "")?.replace(".", "");
    if (!cep || cep.length !== 8) return;

    try {
      const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
      const data = await response.json();

      if (!data.erro) {
        const bairro = data.bairro ? `Bairro ${data.bairro}, ` : "";
        const logradouro = data.logradouro ? `Logradouro ${data.logradouro}` : "";

        methods.setValue("address", `${bairro}${logradouro}`, { shouldValidate: false });
        methods.setValue("city", data.localidade, { shouldValidate: false });
        methods.setValue("state", data.uf, { shouldValidate: false });
      }
    } catch (error) {
      console.error("Erro ao buscar CEP:", error);
    }
  };


  // Referências para evitar loops infinitos
  const planningNumberRef = useRef(planningNumber);
  const formStartDateRef = useRef(formValues.startDate);
  const formEndDateRef = useRef(formValues.endDate);
  const formPeriodicityRef = useRef(formValues.periodicity);

  // Atualizar as referências quando os valores mudarem
  useEffect(() => {
    planningNumberRef.current = planningNumber;
    formStartDateRef.current = formValues.startDate;
    formEndDateRef.current = formValues.endDate;
    formPeriodicityRef.current = formValues.periodicity;
  }, [planningNumber, formValues.startDate, formValues.endDate, formValues.periodicity]);

  // Efeito para calcular o número de períodos
  useEffect(() => {
    // Verificar se temos os valores necessários
    if (!formValues.startDate || !formValues.endDate || formValues.periodicity === "NONE") {
      if (planningNumber !== 0) {
        setPlanningNumber(0);
      }
      return;
    }

    // Calcular o número de períodos com base na periodicidade
    const startDate = new Date(formValues.startDate);
    const endDate = new Date(formValues.endDate);

    // Verificar se as datas são válidas
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return;
    }

    // Verificar se a data de início é posterior à data de fim
    if (startDate > endDate) {
      return; // Não calcular períodos se a data de início for posterior à data de fim
    }

    // Calcular a diferença em dias
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Calcular o número de períodos com base na periodicidade
    let periods = 0;
    if (formValues.periodicity === "WEEKLY") {
      periods = Math.ceil(diffDays / 7); // Semanal
    } else if (formValues.periodicity === "MONTHLY") {
      periods = Math.ceil(diffDays / 30); // Mensal (aproximado)
    }

    // Atualizar o estado apenas se for diferente do atual
    if (periods !== 0 && periods !== planningNumber) {
      setPlanningNumber(periods);
    }
  }, [formValues.startDate, formValues.endDate, formValues.periodicity, planningNumber]);


  // Referência para os dados de planning
  const planningDataRef = useRef(planningData);

  // Atualizar a referência quando os dados mudarem
  useEffect(() => {
    planningDataRef.current = planningData;
  }, [planningData]);

  // Efeito para gerenciar plannings
  useEffect(() => {
    // Verificar se temos os valores necessários
    if (planningNumber <= 0 || formValues.periodicity === "NONE" || !formValues.startDate || !formValues.endDate) {
      if (planningData.length > 0) {
        setPlanningsData([]);
      }
      return;
    }

    // Criar um array de plannings com base no número de períodos
    const newPlannings: PlanningFrequencyItemInterface[] = [];

    // Calcular as datas de início e fim de cada período
    const startDate = new Date(formValues.startDate);
    const endDate = new Date(formValues.endDate);

    // Verificar se as datas são válidas
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return;
    }

    // Verificar se a data de início é posterior à data de fim
    if (startDate > endDate) {
      return; // Não calcular períodos se a data de início for posterior à data de fim
    }

    // Calcular o intervalo entre períodos
    const totalDays = Math.abs(endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    const daysPerPeriod = Math.floor(totalDays / planningNumber);

    // Criar os plannings
    for (let i = 0; i < planningNumber; i++) {
      const periodStartDate = new Date(startDate);
      periodStartDate.setDate(periodStartDate.getDate() + (i * daysPerPeriod));

      const periodEndDate = new Date(periodStartDate);
      periodEndDate.setDate(periodEndDate.getDate() + daysPerPeriod - 1);

      // Ajustar a data de fim do último período para coincidir com a data de fim do projeto
      if (i === planningNumber - 1) {
        periodEndDate.setTime(endDate.getTime());
      }

      // Adicionar o planning
      // Verificar se as datas são válidas
      if (!isNaN(periodStartDate.getTime()) && !isNaN(periodEndDate.getTime())) {
        newPlannings.push({
          order: i + 1,
          content: `${periodStartDate.toISOString().split('T')[0]} - ${periodEndDate.toISOString().split('T')[0]}`,
          label: `Período ${i + 1} (${Math.round(100 / planningNumber)}%)`
        });
      }
    }

    // Atualizar o estado apenas se for diferente do atual
    const currentPlanningsStr = JSON.stringify(planningDataRef.current);
    const newPlanningsStr = JSON.stringify(newPlannings);

    if (currentPlanningsStr !== newPlanningsStr) {
      setPlanningsData(newPlannings);
    }
  }, [planningNumber, formValues.startDate, formValues.endDate, formValues.periodicity, planningData]);


  // Referência para as opções de parcelas
  const installmentsRef = useRef(installments);

  // Atualizar a referência quando os dados mudarem
  useEffect(() => {
    installmentsRef.current = installments;
  }, [installments]);

  // Efeito para calcular opções de parcelas
  useEffect(() => {
    // Verificar se temos os valores necessários
    if (!formValues.budget) return;

    // Gerar as opções de parcelas (de 1 a 42)
    const options: { label: string; value: string }[] = [];
    for (let i = 1; i <= 42; i++) {
      options.push({
        label: `${i} parcela${i > 1 ? 's' : ''}`,
        value: i.toString()
      });
    }

    // Atualizar o estado apenas se for diferente do atual
    const currentOptionsStr = JSON.stringify(installmentsRef.current);
    const newOptionsStr = JSON.stringify(options);

    if (currentOptionsStr !== newOptionsStr) {
      setInstallments(options);
    }
  }, [formValues.budget]);




  // Efeito para definir o valor padrão de 1 parcela quando o usuário seleciona a condição de pagamento "À prazo"
  useEffect(() => {
    // Verificar se a condição de pagamento é parcelada
    if (formValues.paymentCondition !== "INSTALLMENTS") return;

    // Definir 1 parcela como padrão se não estiver definido
    if (!formValues.installmentNumber) {
      methods.setValue("installmentNumber", "1", {
        shouldValidate: false,
        shouldDirty: false
      });
    }
  }, [formValues.paymentCondition, formValues.installmentNumber, methods]);

  // Efeito para calcular as opções de parcelas e atualizar o valor da parcela selecionada
  useEffect(() => {
    // Verificar se temos os valores necessários
    if (!formValues.budget) return;

    // Obter os valores necessários
    const budget = formValues.budget;
    const installmentNumber = formValues.installmentNumber || "1";
    const downPayment = formValues.downPayment || "0";

    try {
      // Calcular o valor da entrada (se existir e for menor que o orçamento)
      const budgetValue = parseCurrencyToNumber(budget.toString());
      const downPaymentValue = parseCurrencyToNumber(downPayment.toString());

      // Garantir que a entrada não seja maior que o orçamento
      const validDownPayment = downPaymentValue < budgetValue ? downPaymentValue : 0;

      // Calcular o valor a ser parcelado
      const budgetWithoutDownPayment = budgetValue - validDownPayment;

      // Gerar as opções de parcelas (de 1 a 42)
      const installmentOptions = Array.from({ length: 42 }, (_, idx) => {
        const number = idx + 1;
        const installmentValue = budgetWithoutDownPayment / number;
        return {
          label: `${number} x ${formatCurrency(installmentValue)}`,
          value: number.toString(),
        };
      });

      // Atualizar as opções de parcelas no estado
      setInstallments(installmentOptions);

      // Se estiver no modo de pagamento parcelado, calcular o valor da parcela
      if (formValues.paymentCondition === "INSTALLMENTS") {
        const selectedInstallmentAmount = budgetWithoutDownPayment / Number(installmentNumber);

        // Atualizar o valor da parcela no formulário
        methods.setValue("installmentAmount", formatCurrency(selectedInstallmentAmount), {
          shouldValidate: false,
          shouldDirty: false
        });
      }
    } catch (error) {
      console.error("Erro ao calcular parcelas:", error);
    }
  }, [formValues.budget, formValues.downPayment, formValues.installmentNumber, formValues.paymentCondition, methods]);


  // Função para normalizar valores de campos problemáticos
  const normalizeFieldValues = (values: any) => {
    if (!values) return values;

    // Criar uma cópia para não modificar o original
    const normalized = { ...values };

    // Função auxiliar para normalizar valores monetários
    const normalizeMoneyValue = (value: any): string => {
      if (value === undefined || value === null) return "";

      let amount = String(value);

      // Se for uma string formatada como moeda (ex: "R$ 1.911,44"), extrair apenas o valor numérico
      if (amount.includes('R$')) {
        // Remover "R$ ", substituir vírgula por ponto e remover pontos de milhar
        amount = amount.replace(/R\$\s?/g, '')
          .replace(/\./g, '')
          .replace(/,/g, '.');
      }

      // Converter para número e depois para string para garantir formato consistente
      const numValue = parseFloat(amount);
      return isNaN(numValue) ? "" : numValue.toString();
    };

    // Normalizar installmentNumber para string
    if (normalized.installmentNumber !== undefined) {
      normalized.installmentNumber = String(normalized.installmentNumber);
    }

    // Normalizar campos monetários
    const moneyFields = ['installmentAmount', 'budget', 'downPayment'];

    moneyFields.forEach(field => {
      if (normalized[field] !== undefined) {
        normalized[field] = normalizeMoneyValue(normalized[field]);
      }
    });

    return normalized;
  };

  // Funções para lidar com ações que requerem confirmação
  const handleActionWithConfirmation = useCallback((actionType: 'cancel' | 'view' | 'send' | 'resend', callback?: () => void) => {
    // Special case for view action - if we have a fileEditorId, allow viewing without confirmation
    // This fixes the issue where the user is asked to save again after just saving
    if (actionType === 'view' && proposalState?.fileEditorId) {
      // Execute the callback directly without checking for unsaved changes
      if (callback) callback();
      return;
    }

    // For other actions, check if there are unsaved changes
    if (methods.formState.isDirty) {
      // Verificar se houve alterações reais comparando com os valores iniciais
      const currentValues = normalizeFieldValues(methods.getValues());
      const initialValues = normalizeFieldValues(methods.formState.defaultValues);

      // Apenas mostrar diálogo se houver diferenças reais
      const hasRealChanges = JSON.stringify(currentValues) !== JSON.stringify(initialValues);

      if (hasRealChanges) {
        // Identificar quais campos foram alterados
        const changedFields: Record<string, { old: any, new: any }> = {};

        // Comparar cada campo para identificar as alterações
        Object.keys(currentValues).forEach(key => {
          const currentValue = currentValues[key];
          const initialValue = initialValues ? initialValues[key] : undefined;

          // Verificar se o valor mudou
          if (JSON.stringify(currentValue) !== JSON.stringify(initialValue)) {
            changedFields[key] = {
              old: initialValue,
              new: currentValue
            };
          }
        });

        // Registrar os campos alterados no console
        console.log('Campos alterados:', changedFields);

        // Log específico para o campo installment
        if (changedFields.installmentNumber || changedFields.installmentAmount) {
          console.log('Detalhes do campo installment (confirmação):', {
            installmentNumber: {
              current: currentValues.installmentNumber,
              initial: initialValues ? initialValues.installmentNumber : undefined,
              currentType: typeof currentValues.installmentNumber,
              initialType: initialValues ? typeof initialValues.installmentNumber : undefined
            },
            installmentAmount: {
              current: currentValues.installmentAmount,
              initial: initialValues ? initialValues.installmentAmount : undefined,
              currentType: typeof currentValues.installmentAmount,
              initialType: initialValues ? typeof initialValues.installmentAmount : undefined
            }
          });
        }

        // Mostrar diálogo de confirmação
        setPendingAction({ type: actionType, callback });
        setShowUnsavedDialog(true);
        return;
      }
    }

    // Se não há alterações reais, executar a ação diretamente
    if (callback) callback();
  }, [methods, proposalState?.fileEditorId]); // Added proposalState?.fileEditorId as dependency

  // Funções de gerenciamento de diálogo de alterações não salvas
  const handleSaveAndContinue = useCallback(async () => {
    // Fechar o diálogo
    setShowUnsavedDialog(false);

    // Salvar o formulário
    const formData = methods.getValues();
    const savedData = await saveData(formData, false, true);

    // Resetar o estado de modificação após salvar
    setFormModified(false);

    // Resetar o formulário para marcar como não modificado, mas manter os valores
    const currentValues = methods.getValues();
    methods.reset(currentValues, {
      keepValues: true,
      keepDirty: false, // Importante: marcar o formulário como não modificado
      keepIsSubmitted: false,
      keepTouched: false
    });

    // Se o salvamento foi bem-sucedido, executar a ação pendente
    if (savedData && pendingAction?.callback) {
      pendingAction.callback();
    }
  }, [methods, pendingAction]);

  const handleContinueWithoutSaving = useCallback(() => {
    setShowUnsavedDialog(false);

    // Resetar o formulário e o estado de modificação ao cancelar
    if (pendingAction?.type === 'cancel') {
      setFormModified(false);
      methods.reset(methods.formState.defaultValues, {
        keepValues: false,
        keepDirty: false,
        keepIsSubmitted: false,
        keepTouched: false
      });
    }

    if (pendingAction?.callback) {
      pendingAction.callback();
    }
  }, [pendingAction, methods]);

  // Efeito para atualizar o template quando a situação mudar para PROPOSAL_ACCEPTED
  useEffect(() => {
    if (proposal?.situation === "PROPOSAL_ACCEPTED" || isContractValidated) {
      // Encontrar um template de contrato disponível
      const contractTemplate = templatesFiltered.find(t => t.type === "CONTRACT");

      if (contractTemplate && !methods.getValues("proposalTemplateId")) {
        console.log('[DEBUG] Atualizando proposalTemplateId para template de contrato:', contractTemplate.id);
        // Atualizar o template selecionado apenas se não houver um template já selecionado
        methods.setValue("proposalTemplateId", contractTemplate.id);

        // Salvar a proposta com o novo template
        const proposalData = {
          ...methods.getValues(),
          id: proposal?.id,
          proposalTemplateId: contractTemplate.id,
          plannings: planningFormRef.current?.planningForms || [],
          workTotalCost: 0
        };

        // Salvar em segundo plano
        setTimeout(async () => {
          try {
            await saveData(proposalData, true, true);
          } catch (error) {
            console.error("Erro ao salvar proposta após mudança de template:", error);
          }
        }, 500);
      }
    }
  }, [proposal?.situation, isContractValidated, templatesFiltered]);

  // Novo efeito: Limpar o template SOMENTE quando a situação mudar para PROPOSAL_ACCEPTED ou isContractValidated mudar para true
  const prevSituationRef = useRef(proposal?.situation);
  const prevIsContractValidatedRef = useRef(isContractValidated);
  // Ref para garantir que o campo só seja limpo uma vez por transição
  const templateClearedRef = useRef(false);

  useEffect(() => {
    const prevSituation = prevSituationRef.current;
    const prevIsContractValidated = prevIsContractValidatedRef.current;

    // Só limpa se a situação mudou para PROPOSAL_ACCEPTED ou isContractValidated mudou para true
    const situationBecameAccepted = prevSituation !== "PROPOSAL_ACCEPTED" && proposal?.situation === "PROPOSAL_ACCEPTED";
    const contractValidatedBecameTrue = !prevIsContractValidated && isContractValidated;

    // Só limpa se não houver um template de contrato já selecionado
    const currentTemplateId = methods.getValues("proposalTemplateId");
    const isCurrentTemplateContract = templatesFiltered.some(t => t.id === currentTemplateId && t.type === "CONTRACT");

    if ((situationBecameAccepted || contractValidatedBecameTrue) && !templateClearedRef.current && !isCurrentTemplateContract) {
      console.log('[DEBUG] Limpando proposalTemplateId por transição para contrato:', { currentTemplateId, isCurrentTemplateContract, situationBecameAccepted, contractValidatedBecameTrue });
      methods.setValue("proposalTemplateId", "");
      templateClearedRef.current = true;
      // Só salva se customerId estiver preenchido e não for string vazia
      const currentValues = methods.getValues();
      if (
        currentValues.customerId &&
        typeof currentValues.customerId === "string" &&
        currentValues.customerId.trim() !== ""
      ) {
        const proposalData = {
          ...currentValues,
          id: proposal?.id,
          proposalTemplateId: "",
          plannings: planningFormRef.current?.planningForms || [],
          workTotalCost: 0
        };
        setTimeout(async () => {
          try {
            await saveData(proposalData, true, true);
          } catch (error) {
            console.error("Erro ao salvar proposta após limpar template:", error);
          }
        }, 500);
      } else {
        console.warn("Tentativa de salvar proposta sem customerId válido");
      }
    } else {
      console.log('[DEBUG] Não limpou proposalTemplateId por transição para contrato:', { currentTemplateId, isCurrentTemplateContract, situationBecameAccepted, contractValidatedBecameTrue });
    }

    // Se a situação voltou para algo diferente de contrato, permite limpar novamente numa próxima transição
    if (proposal?.situation !== "PROPOSAL_ACCEPTED" && !isContractValidated) {
      templateClearedRef.current = false;
    }

    // Atualiza os valores anteriores
    prevSituationRef.current = proposal?.situation;
    prevIsContractValidatedRef.current = isContractValidated;
  }, [proposal?.situation, isContractValidated, templatesFiltered, methods]);

  // Função utilitária para verificar alterações reais
  const hasRealFormChanges = () => {
    const currentValues = normalizeFieldValues(methods.getValues());
    const initialValues = normalizeFieldValues(methods.formState.defaultValues);
    return JSON.stringify(currentValues) !== JSON.stringify(initialValues);
  };

  return (
    <div className="flex flex-col h-full justify-between max-md:proposal-form-container">
      {/* Diálogo de confirmação para alterações não salvas */}
      <UnsavedChangesDialog
        isOpen={showUnsavedDialog}
        onClose={() => setShowUnsavedDialog(false)}
        onSaveAndContinue={handleSaveAndContinue}
        onContinueWithoutSaving={handleContinueWithoutSaving}
        actionName={pendingAction?.type === 'cancel' ? 'sair' :
          pendingAction?.type === 'view' ? 'visualizar' :
            pendingAction?.type === 'send' ? 'enviar' : 'reenviar'}
      />
      <AlertDialog open={showOverwriteDialog} onOpenChange={setShowOverwriteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Atenção</AlertDialogTitle>
            <AlertDialogDescription>
              O formulário passou por alterações e, por isso, as modificações feitas no documento serão revertidas para o padrão do template original.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={async () => {
                setShowOverwriteDialog(false);
                if (pendingSubmit) {
                  await handleSubmit(pendingSubmit.skipLoading, true); // skipToast = true para não mostrar o dialog novamente
                  setPendingSubmit(null);
                }
              }}
            >
              Confirmar
            </AlertDialogAction>
            <AlertDialogAction
              asChild
              onClick={() => {
                setShowOverwriteDialog(false);
                setPendingSubmit(null);
              }}
            >
              <button type="button" className="ml-2">Cancelar</button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Atenção</AlertDialogTitle>
            <AlertDialogDescription>
              O formulário passou por alterações e, por isso, as modificações feitas no documento serão revertidas para o padrão do template original. Deseja continuar e visualizar?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col sm:flex-row gap-3 mt-2">
            <Button
              variant="outline"
              onClick={() => {
                setShowViewDialog(false);
                setPendingView(null);
              }}
              className="sm:order-1 border-gray-300 hover:bg-gray-100 hover:text-gray-800"
            >
              Cancelar
            </Button>
            <Button
              onClick={async () => {
                setShowViewDialog(false);
                if (pendingView) {
                  await pendingView();
                  setPendingView(null);
                }
              }}
              className="bg-green-500 hover:bg-green-600 sm:order-2 transition-all duration-200"
            >
              Confirmar
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <h1 className="text-2xl font-bold text-green-500">
        {(() => {
          if (isContractValidated) return "Contrato";

          // Check if we have a proposal state with ID (meaning it's been saved)
          if (proposalState?.id) {
            switch (proposalState.situation) {
              case "UNDER_ANALYSIS":
                return "Analisar Proposta";
              case "PROPOSAL_SENT":
                return "Aguardando Resposta";
              case "PROPOSAL_ACCEPTED":
                return "Contrato";
              default:
                return "Editar Proposta";
            }
          } else {
            // If no proposal state with ID, it's a new proposal
            return "Nova Proposta";
          }
        })()}
      </h1>


      <main
        key={renderKey} // Chave para forçar re-renderização
        className="mt-5 mb-0 flex flex-col gap-3 px-2 overflow-y-auto relative flex-grow thin-scrollbar"
        style={{ height: "calc(100svh - 16rem)", paddingBottom: typeof window !== 'undefined' && window.innerWidth <= 768 ? "50px" : "0" }} // Altura relativa ao viewport com padding extra apenas no mobile
      >
        {(loading || isLoading) && (
          <div className="absolute inset-0 bg-white/80 z-50 flex items-center justify-center">
            <div className="flex flex-col items-center gap-4">
              <div className="size-10 border-4 border-green-500 border-t-transparent rounded-full animate-spin" />
              <p className="text-sm font-medium text-gray-600">{loading ? "Salvando proposta e gerando arquivo..." : "Carregando dados..."}</p>
            </div>

          </div>
        )}
        {!dataLoaded && (
          <div className="flex flex-col items-center justify-center py-10">
            <div className="size-10 border-4 border-green-500 border-t-transparent rounded-full animate-spin" />
            <p className="mt-4 text-gray-500">Carregando dados do formulário...</p>
          </div>
        )}

        {dataLoaded && (!customers?.length || !templatesFiltered?.length || !scopes?.length) && (
          <div className="bg-yellow-50 p-4 rounded-md mb-4 border border-yellow-200">
            <h3 className="text-yellow-800 font-medium mb-2">Atenção</h3>
            <p className="text-yellow-700 mb-2">Alguns dados necessários para o formulário não foram carregados:</p>
            <ul className="list-disc pl-5 mb-3 text-yellow-700">
              {!customers?.length && <li>Clientes</li>}
              {!templatesFiltered?.length && <li>Templates</li>}
              {!scopes?.length && <li>Escopos de serviço</li>}
            </ul>
            <p className="text-yellow-700 mb-3">Verifique se esses dados existem no sistema ou tente recarregar a página.</p>
          </div>
        )}
        {dataLoaded && (
          <FormProvider {...methods}>
            <form className="flex flex-col gap-3">
              <CustomInput
                label={`${isContractValidated ? "Tipo de Contrato" : "Tipo de proposta"}`}
                name="name"
                placeholder="Nome"
                disabled={isFormDisabled}
                required={true}
              />


              <div className="relative">
                <Controller
                  name="customerId"
                  control={methods.control}
                  render={({ field, fieldState }) => {
                    // Remover logs para melhorar a performance
                    // console.log('Renderizando Select de cliente:', {
                    //   fieldValue: field.value,
                    //   customerId: proposal?.customerId,
                    //   customersLength: customers?.length,
                    //   fieldState,
                    //   isSubmitted: methods.formState.isSubmitted,
                    //   errors: methods.formState.errors
                    // });

                    // Forçar o valor do campo se estiver vazio mas a proposta tiver um cliente
                    const hasSetCustomerRef = useRef(false);

                    // Executar apenas uma vez na montagem do componente ou quando a proposta mudar
                    useEffect(() => {
                      // Se já definimos o valor do cliente, não definir novamente
                      if (hasSetCustomerRef.current) return;

                      // Verificar se temos uma proposta com customerId e se o cliente existe nas opções carregadas
                      if (!field.value && proposal?.customerId && customerOptions?.length > 0) {
                        const customerExists = customerOptions.some(c => c.value === proposal.customerId);

                        console.log('Verificando cliente da proposta no Controller:', {
                          proposalCustomerId: proposal.customerId,
                          fieldValue: field.value,
                          customerOptionsCount: customerOptions.length,
                          customerExists,
                          firstFewOptions: customerOptions.slice(0, 3).map(c => ({ value: c.value, label: c.label }))
                        });

                        if (customerExists) {
                          console.log('Definindo cliente da proposta no Controller:', {
                            customerId: proposal.customerId,
                            customerFound: customerExists
                          });
                          field.onChange(proposal.customerId);
                          hasSetCustomerRef.current = true;
                        } else {
                          console.warn('Cliente da proposta não encontrado nas opções do Controller:', {
                            customerId: proposal.customerId,
                            availableOptions: customerOptions.map(c => ({ value: c.value, label: c.label }))
                          });
                        }
                      }
                    }, [proposal?.customerId, customerOptions, field]);  // Usar customerOptions em vez de customers

                    // Determinar se deve mostrar o erro de validação
                    // Mostrar em qualquer uma destas condições:
                    // 1. O campo foi tocado e é inválido
                    // 2. O formulário foi submetido e o campo é inválido
                    // 3. Existe um erro específico para este campo
                    // 4. A variável no localStorage indica que o formulário foi submetido
                    const hasError = !!methods.formState.errors.customerId;
                    const isSubmitted = methods.formState.isSubmitted;
                    const isTouched = fieldState.isTouched;
                    const isInvalid = fieldState.invalid;

                    // Verificar se o formulário foi submetido através do localStorage
                    const wasFormSubmitted = typeof window !== 'undefined' && localStorage.getItem('proposal_form_submitted') === 'true';

                    // Condição para mostrar erro
                    let showError = false;
                    if ((isInvalid && (isTouched || isSubmitted || wasFormSubmitted)) || hasError) {
                      showError = true;
                    }

                    // console.log('Estado de validação do cliente:', {
                    //   hasError,
                    //   isSubmitted,
                    //   isTouched,
                    //   isInvalid,
                    //   wasFormSubmitted,
                    //   showError
                    // });

                    return (
                      <div className="space-y-1">
                        <Label className="font-bold text-gray-700">Vincular cliente</Label>
                        <Combobox
                          options={customerOptions}
                          value={field.value || ""}
                          onChange={(value) => {
                            console.log('Cliente selecionado:', {
                              value,
                              customer: customerOptions.find(c => c.value === value)
                            });
                            field.onChange(value);
                            // Marcar o campo como tocado quando o usuário selecionar um valor
                            field.onBlur();
                          }}
                          placeholder="Escolha um cliente"
                          searchPlaceholder="Digite o nome do cliente..."
                          emptyMessage="Nenhum cliente encontrado"
                          onSearch={searchCustomers}
                          disabled={isFormDisabled}
                          className={`w-full h-10 ${showError ? 'border-red-500' : ''}`}
                        />
                        {showError && (
                          <div className="text-destructive text-[0.8rem] font-bold mt-1">Cliente é obrigatório</div>
                        )}
                        {loadingCustomers && (
                          <div className="text-blue-600 text-[0.8rem] font-medium mt-1">
                            Carregando clientes...
                          </div>
                        )}
                        {!loadingCustomers && !customerOptions?.length && (
                          <div className="text-destructive text-[0.8rem] font-medium mt-1">
                            Nenhum cliente encontrado. Verifique se existem clientes cadastrados.
                          </div>
                        )}
                      </div>
                    );
                  }}
                />
              </div>


              {/* Tipo de Serviço - Mostrar sempre para filtrar templates adequadamente */}
              <TwoColumnRadioGroup
                name="serviceType"
                label="Tipo de Serviço"
                value={formValues.serviceType}
                items={[
                  { label: "Inspeção", value: "INSPECAO" },
                  { label: "Fiscalização", value: "FISCALIZACAO" },
                  { label: "Gerenciamento", value: "GERENCIAMENTO" },
                  // { label: "Consultoria", value: "CONSULTORIA" },
                  { label: "Projeto", value: "PROJECT" },
                  { label: "Consultoria", value: "CONSULTANCY" }
                ]}
                disabled={isFormDisabled}
                required={isContractValidated || shouldShowContractTemplates()}
              />

              <div className="relative">
                <div className="grid gap-2">
                  <div className="flex mb-2">
                    <Label htmlFor="proposalTemplateId" className="font-bold text-gray-700">
                      {`Vincular template de ${isContractValidated || shouldShowContractTemplates() ? "contrato" : "proposta"}`}
                    </Label>
                  </div>

                  <Controller
                    name="proposalTemplateId"
                    control={methods.control}
                    render={({ field, fieldState }) => {
                      // Forçar o valor do campo se estiver vazio mas a proposta tiver um template
                      const hasSetTemplateRef = useRef(false);

                      // Executar apenas uma vez na montagem do componente ou quando a proposta mudar
                      useEffect(() => {
                        // Se já definimos o valor do template, não definir novamente
                        if (hasSetTemplateRef.current) return;

                        console.log("Verificando se deve definir template:", {
                          hasValue: !!field.value,
                          proposalTemplateId: proposal?.proposalTemplateId,
                          templatesFilteredCount: templatesFiltered?.length,
                          isContractValidated: isContractValidated,
                          situation: proposal?.situation
                        });

                        // Se não estamos em modo de contrato, podemos usar o template da proposta
                        if (!isContractValidated && proposal?.situation !== "PROPOSAL_ACCEPTED") {
                          // Verificar se o template da proposta está nos templates filtrados
                          const templateExists = proposal?.proposalTemplateId &&
                            templatesFiltered?.some(t => t.id === proposal.proposalTemplateId);

                          if (!field.value && proposal?.proposalTemplateId && templateExists) {
                            console.log("Definindo template da proposta:", proposal.proposalTemplateId);
                            field.onChange(proposal.proposalTemplateId);
                            hasSetTemplateRef.current = true;
                          }
                        } else {
                          // Se estamos em modo de contrato, não force o campo a ficar vazio.
                          hasSetTemplateRef.current = true;
                        }
                      }, [proposal?.proposalTemplateId, templatesFiltered, field, proposal?.situation, isContractValidated]);

                      // Determinar se deve mostrar o erro de validação
                      // Para contratos, só mostrar erro quando o formulário for realmente submetido
                      const hasError = !!methods.formState.errors.proposalTemplateId;
                      const isSubmitted = methods.formState.isSubmitted;
                      const isTouched = fieldState.isTouched;
                      const isInvalid = fieldState.invalid;

                      // Verificar se o formulário foi submetido através do localStorage
                      const wasFormSubmitted = typeof window !== 'undefined' && localStorage.getItem('proposal_form_submitted') === 'true';

                      // Condição para mostrar erro
                      let showError = false;

                      // Para contratos, só mostrar erro na submissão real do formulário
                      if (isContractValidated || proposal?.situation === "PROPOSAL_ACCEPTED") {
                        showError = (isInvalid && (isSubmitted || wasFormSubmitted)) || hasError;
                      } else {
                        // Para propostas normais, manter o comportamento original
                        showError = (isInvalid && (isTouched || isSubmitted || wasFormSubmitted)) || hasError;
                      }

                      // console.log('Estado de validação do template:', {
                      //   hasError,
                      //   isSubmitted,
                      //   isTouched,
                      //   isInvalid,
                      //   wasFormSubmitted,
                      //   showError
                      // });

                      return (
                        <div className="space-y-1">
                          <Select
                            value={field.value || proposal?.proposalTemplateId}
                            onValueChange={(value) => {
                              console.log('[DEBUG] Alterando proposalTemplateId via Select:', value);
                              field.onChange(value);
                              // Para contratos, não marcar como tocado automaticamente para evitar validação prematura
                              if (!isContractValidated && proposal?.situation !== "PROPOSAL_ACCEPTED") {
                                field.onBlur();
                              }

                              // Verificar se estamos em modo de contrato
                              const isContractMode = proposal?.situation === "PROPOSAL_ACCEPTED" || isContractValidated;

                              if (isContractMode) {
                                // Salvar a proposta automaticamente ao selecionar um template de contrato
                                const proposalData = {
                                  ...formValues,
                                  id: proposal?.id,
                                  proposalTemplateId: value,
                                  plannings: planningFormRef.current?.planningForms || [],
                                  workTotalCost: 0
                                };

                                // Salvar a proposta em segundo plano
                                setTimeout(async () => {
                                  try {
                                    await saveData(proposalData, true, true);
                                  } catch (error) {
                                    console.error("Erro ao salvar proposta após selecionar template de contrato:", error);
                                  }
                                }, 500);
                              }
                            }}
                            disabled={isFormDisabled}
                          >
                            <SelectTrigger className={`w-full h-10 ${showError ? 'border-red-500' : ''}`}>
                              <SelectValue placeholder="Escolha um template" />
                            </SelectTrigger>
                            <SelectContent>
                              {templatesFiltered?.length ?
                                templatesFiltered.map((template) => (
                                  <SelectItem key={template.id} value={template.id || ''}>
                                    {template.title || 'Sem título'}
                                    {template.type === "CONTRACT" ? " (Contrato)" : ""}
                                  </SelectItem>
                                )) : []
                              }
                            </SelectContent>
                          </Select>
                          {showError && (
                            <div className="text-destructive text-[0.8rem] font-bold mt-1">Template é obrigatório</div>
                          )}
                        </div>
                      );
                    }}
                  />
                </div>

                {!templatesFiltered?.length && (
                  <div className="text-red-500 text-sm mt-1">
                    Nenhum template encontrado. Verifique se existem templates cadastrados.
                  </div>
                )}
              </div>




              <div className="flex flex-col sm:flex-row gap-3">
                <CustomInput
                  label="Data inicio"
                  name="startDate"
                  type="date"
                  className="w-full"
                  placeholder="Data inicio"
                  hideErrorMessage={true}
                  disabled={isFormDisabled}
                  required={true}
                />
                <CustomInput
                  label="Data fim prevista"
                  name="endDate"
                  type="date"
                  className="w-full"
                  placeholder="Data fim prevista"
                  hideErrorMessage={true}
                  disabled={isFormDisabled}
                  required={true}
                />
              </div>


              <Controller
                name="area"
                control={methods.control}
                render={({ field, fieldState }) => {
                  // Iniciar com 0 para garantir que o campo não esteja vazio
                  const value = field.value !== undefined ? field.value.toString() : '0';
                  const hasError = fieldState.invalid;
                  // Verificar se o formulário foi submetido através do localStorage
                  const wasFormSubmitted = typeof window !== 'undefined' && localStorage.getItem('proposal_form_submitted') === 'true';

                  const showError = hasError && (fieldState.isTouched || methods.formState.isSubmitted || wasFormSubmitted);

                  return (
                    <div className="grid gap-2">
                      <div className="flex mb-2">
                        <Label htmlFor="area" className="font-bold text-gray-700">
                          Área em m²
                        </Label>
                      </div>
                      <div className="relative">
                        <input
                          type="number"
                          id="area"
                          min={0}
                          value={value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            field.onBlur();
                          }}
                          disabled={isFormDisabled}
                          className={`flex h-10 w-full rounded-md border ${(showError || (value !== '' && parseFloat(value) <= 0) || (methods.formState.isSubmitted && !value)) ? 'border-red-500' : 'border-input'} bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
                        />
                        {(showError && value === '') || (methods.formState.isSubmitted && !value) && (
                          <div className="text-destructive text-[0.8rem] font-bold mt-1">Área é obrigatório</div>
                        )}
                        {value !== '' && parseFloat(value) === 0 && (
                          <div className="text-destructive text-[0.8rem] font-bold mt-1">Área é obrigatório</div>
                        )}
                        {value !== '' && parseFloat(value) < 0 && (
                          <div className="text-destructive text-[0.8rem] font-bold mt-1">Área deve ser maior que 0</div>
                        )}
                      </div>
                    </div>
                  );
                }}
              />


              <div className="flex flex-row gap-3 items-center">
                <CustomInput
                  label="CEP"
                  name="cep"
                  placeholder="00.000-000"
                  type="mask"
                  mask="00.000-000"
                  className="w-[70%]"
                  disabled={isFormDisabled}
                />


                <div className="h-full flex items-end w-[30%]">
                  <Button
                    type="button"
                    className="bg-blue-500 hover:bg-blue-600 w-full"
                    style={{ height: "40px" }}
                    onClick={searchAddresByCep}
                    disabled={isFormDisabled}
                  >
                    <Search />
                  </Button>
                </div>
              </div>


              <CustomInput
                label="Endereço"
                name="address"
                placeholder="Endereço"
                disabled={isFormDisabled}
              />


              <div className="flex flex-col sm:flex-row gap-3">
                <CustomInput
                  label="Cidade"
                  name="city"
                  placeholder="Cidade"
                  className="w-[70%]"
                  disabled={isFormDisabled}
                />


                <CustomInput
                  label="Estado"
                  name="state"
                  placeholder="Estado"
                  type="select"
                  className="w-[30%]"
                  items={states}
                  disabled={isFormDisabled}
                />
              </div>
              <Controller
                name="budget"
                control={methods.control}
                render={({ field }) => (
                  <CurrencyInput
                    label="Orçamento da proposta"
                    value={field.value === undefined || field.value === '' ? undefined : parseCurrencyToNumber(field.value)}
                    name="budget"
                    onChange={field.onChange}
                    onBlur={field.onBlur}
                    disabled={isFormDisabled}
                    required={true}
                    isFormSubmitted={methods.formState.isSubmitted}
                    isTouched={methods.formState.touchedFields.budget ? true : false}
                  />
                )}
              />
              <CustomInput
                label="Condição de pagamento"
                name="paymentCondition"
                placeholder="Selecione condição de pagamento"
                type="select"
                items={[
                  { value: "CASH", label: "À vista" },
                  { value: "INSTALLMENTS", label: "À prazo" },
                ]}
                disabled={isFormDisabled}
              />


              {formValues.paymentCondition == "INSTALLMENTS" && (
                <>
                  <Controller
                    name="downPayment"
                    control={methods.control}
                    render={({ field }) => {
                      // Garantir que o valor seja capturado corretamente
                      const handleChange = (value: number) => {
                        field.onChange(value.toString());
                        // Atualizar o formValues imediatamente
                        formValues.downPayment = value.toString();
                      };

                      // Validação: entrada não pode ser maior ou igual ao orçamento quando for à prazo
                      let downPaymentError: string | undefined = undefined;
                      if (formValues.paymentCondition === "INSTALLMENTS") {
                        const budgetValue = parseCurrencyToNumber(formValues.budget?.toString() || '0');
                        const downPaymentValue = parseCurrencyToNumber(field.value === undefined || field.value === '' ? '0' : field.value.toString());
                        if (downPaymentValue >= budgetValue && budgetValue > 0) {
                          downPaymentError = "O valor de entrada não pode ser maior ou igual ao valor do orçamento.";
                        }
                      }

                      return (
                        <CurrencyInput
                          label="Entrada"
                          value={field.value === undefined || field.value === '' ? undefined : parseCurrencyToNumber(field.value)}
                          name="downPayment"
                          onChange={handleChange}
                          onBlur={field.onBlur}
                          disabled={isFormDisabled}
                          maxValue={parseCurrencyToNumber(formValues.budget?.toString() || '0')}
                          errorMessage={downPaymentError ? downPaymentError : ""}
                          isFormSubmitted={methods.formState.isSubmitted}
                          isTouched={methods.formState.touchedFields.downPayment ? true : false}
                        />
                      );
                    }}
                  />


                  <Controller
                    name="installmentNumber"
                    control={methods.control}
                    defaultValue="1"
                    render={({ field }) => (
                      <CustomInput
                        label="Número de Parcelas"
                        name="installmentNumber"
                        placeholder="Escolha o número de parcelas"
                        type="select"
                        items={installments}
                        disabled={isFormDisabled}
                        defaultValue="1"
                        value={field.value || "1"}
                      />
                    )}
                  />
                </>
              )}


              <CustomInput
                label="Metodologias"
                name="methodology"
                type="tag-input"
                disabled={isFormDisabled}
              />


              <CustomInput
                name="serviceScopes"
                label="Escopo de serviços"
                type="checkbox-group"
                hideErrorMessage={true}
                items={scopes?.length ? scopes.map((scope) => {
                  const scopeName = scope.name || 'Sem nome';
                  const truncatedName = scopeName.length > 30 ? `${scopeName.substring(0, 30)}...` : scopeName;
                  return {
                    label: truncatedName,
                    value: scope.id || '',
                    title: scopeName // Para o tooltip
                  };
                }) : []}
                disabled={isFormDisabled}
                required={false}
              />


              <CustomInput
                name="periodicity"
                label="Periodicidade do cronograma"
                type="radio-group"
                value={formValues.periodicity}
                items={[
                  { label: "Mensal", value: "MONTHLY" },
                  { label: "Semanal", value: "WEEKLY" },
                  { label: "Sem cronograma", value: "NONE" },
                ]}
                disabled={isFormDisabled}
              />
            </form>

            {formValues.periodicity !== "NONE" && !!planningNumber && (
              <PlanningForms
                planningNumber={planningNumber}
                periodicity={formValues.periodicity as Periodicity}
                plannings={planningData}
                ref={planningFormRef}
                disabled={isFormDisabled}
              />
            )}
          </FormProvider>
        )}
      </main>
      <div key={`buttons-${renderKey}`} className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-5 mb-4 px-2 sm:pb-2 pb-8">
        {/* Botão Cancelar/Voltar - exibido em todas as situações */}
        <Button
          type="button"
          variant="outline"
          className="w-full"
          onClick={() => handleActionWithConfirmation('cancel', onCancelClick)}
          disabled={isFormDisabled || loading || sendingToClient}
        >
          {isEditing ? "Voltar" : "Cancelar"}
        </Button>

        {/* Renderização condicional do botão Visualizar com base no estado fileEditorIdAvailable */}
        {/* Usando renderKey para forçar re-renderização */}
        {(fileEditorIdAvailable && proposalState?.fileEditorId) ? (
          <Button
            onClick={async () => {
              if (hasRealFormChanges()) {
                setShowViewDialog(true);
                setPendingView(() => async () => {
                  // Salva e visualiza (apenas se o usuário confirmar)
                  try {
                    const savedData = await handleSubmit(true, true);
                    if (savedData && savedData.fileEditorId) {
                      const referrerInfo = {
                        url: window.location.pathname + window.location.search,
                        proposalId: proposalState.id,
                        isEditing: true,
                        shouldOpenSheet: true
                      };
                      sessionStorage.setItem('documentEditorReferrer', JSON.stringify(referrerInfo));
                      router.push(`/document-editor/${savedData.fileEditorId}`);
                    } else {
                      showError("Erro", "Não foi possível gerar o arquivo da proposta. Tente novamente.");
                    }
                  } catch (error) {
                    console.error("Erro ao salvar proposta antes de visualizar:", error);
                    showError("Erro", "Ocorreu um erro ao salvar a proposta. Tente novamente.");
                  }
                });
              } else if (proposalState?.fileEditorId) {
                // Se não há alterações reais, apenas visualiza
                const referrerInfo = {
                  url: window.location.pathname + window.location.search,
                  proposalId: proposalState.id,
                  isEditing: true,
                  shouldOpenSheet: true
                };
                sessionStorage.setItem('documentEditorReferrer', JSON.stringify(referrerInfo));
                router.push(`/document-editor/${proposalState.fileEditorId}`);
              }
            }}
            className="gap-2 bg-blue-500 hover:bg-blue-600 flex-shrink-0"
            type="button"
            disabled={sendingToClient || loading}
          >
            Visualizar
          </Button>
        ) : (
          <Button
            className="gap-2 bg-gray-300 hover:bg-gray-300 cursor-not-allowed flex-shrink-0"
            type="button"
            disabled={true}
            title="O arquivo da proposta ainda não foi gerado"
          >
            Visualizar
          </Button>
        )}


        {/* Botão de salvar/atualizar para todas as situações */}
        <Button
          className="bg-green-500 hover:bg-green-600 w-full"
          type="button"
          onClick={async () => {
            // Validar o formulário antes de iniciar o carregamento
            methods.trigger();
            const formValid = proposalsSchema.safeParse(formValues).success;

            // Validar os campos do cronograma se a periodicidade não for "NONE"
            let planningsValid = true;
            if (formValues.periodicity !== "NONE" && planningFormRef.current) {
              planningsValid = planningFormRef.current.validate();

              // Se o cronograma não for válido, mostrar mensagem de erro
              if (!planningsValid) {
                toast({
                  title: "Atenção",
                  description: "Preencha todos os campos obrigatórios do cronograma.",
                  variant: "destructive"
                });
                return;
              }
            }

            // Verificar se o formulário principal é válido
            if (!formValid) {
              // Coletar os erros do formulário
              const errors = methods.formState.errors;

              // Mapear os nomes dos campos para nomes mais amigáveis
              const fieldNames: Record<string, string> = {
                name: "Tipo da proposta",
                customerId: "Cliente",
                proposalTemplateId: "Template",
                startDate: "Data de início",
                endDate: "Data de fim",
                budget: "Orçamento",
                area: "Área",
                serviceScopes: "Escopo de serviços",
                serviceType: "Tipo de serviço",
                installmentNumber: "Número de parcelas"
              };

              // Criar uma lista de campos com erro
              const errorFields = Object.keys(errors)
                .map(field => fieldNames[field] || field)
                .join(", ");

              // Mostrar mensagem de erro com os campos específicos
              if (errorFields) {
                showError("Atenção", `Preencha os seguintes campos obrigatórios: ${errorFields}`);
              } else {
                showError("Atenção", "Preencha todos os campos obrigatórios do formulário.");
              }
              return;
            }

            // Iniciar o carregamento
            setLoading(true);

            // Salvar a proposta com regeneração forçada do arquivo
            const savedData = await handleSubmit(true, true, true); // skipLoading=true, skipToast=true, forceRegenerateFile=true

            // Resetar o estado de modificação após salvar
            setFormModified(false);

            // Resetar o formulário para marcar como não modificado, mas manter os valores
            const currentValues = methods.getValues();

            // Ensure we're using the most up-to-date values if savedData exists
            const updatedValues = savedData ? {
              ...currentValues,
              id: savedData.id, // Make sure the ID is set
              fileEditorId: savedData.fileEditorId // Make sure the fileEditorId is set
            } : currentValues;

            // Reset the form with the updated values
            methods.reset(updatedValues, {
              keepValues: true,
              keepDirty: false, // Importante: marcar o formulário como não modificado
              keepIsSubmitted: false,
              keepTouched: false
            });

            // Force the form to be marked as pristine (not dirty)
            setTimeout(() => {
              if (methods.formState.isDirty) {
                console.log("Forcing form to be marked as not dirty");
                methods.formState.isDirty = false;
              }
            }, 0);

            if (savedData && savedData.id) {
              // Verificar se o arquivo foi gerado
              let attempts = 0;
              const maxAttempts = 20; // Aumentado para 20 tentativas (20 segundos)

              console.log("Iniciando verificação periódica do arquivo da proposta...", {
                id: savedData.id,
                isContractValidated: isContractValidated,
                situation: savedData.situation,
                proposalTemplateId: savedData.proposalTemplateId
              });

              // Função para verificar se o arquivo foi gerado
              const checkFile = async () => {
                try {
                  console.log(`Tentativa ${attempts + 1} de verificar arquivo...`);

                  // Verificar se o ID da proposta é válido
                  if (!savedData.id) {
                    console.error("ID da proposta inválido:", savedData.id);
                    setLoading(false);
                    toast({
                      title: "Erro",
                      description: "ID da proposta inválido. Tente novamente.",
                      variant: "destructive"
                    });
                    return true; // Parar de tentar
                  }

                  const updatedProposal = await getProposalById(savedData.id);

                  console.log("Resultado da verificação:", {
                    found: !!updatedProposal,
                    hasFileEditorId: !!updatedProposal?.fileEditorId,
                    fileEditorId: updatedProposal?.fileEditorId
                  });

                  if (updatedProposal && updatedProposal.fileEditorId) {
                    console.log('Arquivo encontrado!', updatedProposal.fileEditorId);

                    // Arquivo encontrado, atualizar o estado usando a função reloadProposal
                    await reloadProposal(savedData.id);

                    // Atualizar o estado da proposta original
                    if (onChange) {
                      onChange();
                    }

                    // Mostrar mensagem de sucesso
                    showSuccess("Sucesso", "Proposta atualizada e arquivo gerado com sucesso!");

                    // Aguardar um momento para garantir que o estado foi atualizado
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Resetar o formulário para marcar como não modificado, mas manter os valores
                    // Include the updated proposal data
                    const updatedValues = {
                      ...formValues,
                      id: updatedProposal.id,
                      fileEditorId: updatedProposal.fileEditorId
                    };

                    methods.reset(updatedValues, {
                      keepValues: true,
                      keepDirty: false,
                      keepIsSubmitted: false,
                      keepTouched: false
                    });

                    // Force the form to be marked as pristine (not dirty)
                    setTimeout(() => {
                      if (methods.formState.isDirty) {
                        console.log("Forcing form to be marked as not dirty after file found");
                        methods.formState.isDirty = false;
                      }
                    }, 0);

                    // Arquivo encontrado
                    setLoading(false);

                    // Atualizar o estado que controla a visibilidade do botão Visualizar
                    setFileEditorIdAvailable(true);

                    // Forçar re-renderização novamente após o loading ser desativado
                    setRenderKey(prev => prev + 1);

                    return true;
                  }

                  // Incrementar tentativas
                  attempts++;
                  console.log(`Arquivo ainda não encontrado. Tentativa ${attempts} de ${maxAttempts}`);

                  if (attempts >= maxAttempts) {
                    // Limite de tentativas atingido
                    setLoading(false);

                    // Verificar se é um contrato
                    if (isContractValidated || savedData.situation === "PROPOSAL_ACCEPTED") {
                      toast({
                        title: "Atenção",
                        description: "Não foi possível gerar o arquivo do contrato. Tente novamente mais tarde ou entre em contato com o suporte.",
                        variant: "destructive"
                      });
                    } else {
                      toast({
                        title: "Atenção",
                        description: "Não foi possível gerar o arquivo da proposta. Tente novamente mais tarde.",
                        variant: "destructive"
                      });
                    }

                    // Mesmo sem arquivo, a proposta foi salva, então resetar o formulário
                    // Include the saved proposal data
                    const updatedValues = {
                      ...formValues,
                      id: savedData.id
                    };

                    methods.reset(updatedValues, {
                      keepValues: true,
                      keepDirty: false,
                      keepIsSubmitted: false,
                      keepTouched: false
                    });

                    // Force the form to be marked as pristine (not dirty)
                    setTimeout(() => {
                      if (methods.formState.isDirty) {
                        console.log("Forcing form to be marked as not dirty after max attempts");
                        methods.formState.isDirty = false;
                      }
                    }, 0);

                    return true;
                  }

                  // Continuar tentando
                  return false;
                } catch (error) {
                  console.error("Erro ao verificar arquivo da proposta:", error);
                  setLoading(false);

                  // Mostrar mensagem de erro específica
                  toast({
                    title: "Erro",
                    description: "Ocorreu um erro ao verificar o arquivo da proposta. A proposta foi salva, mas o arquivo pode não estar disponível.",
                    variant: "destructive"
                  });

                  // Mesmo com erro, a proposta foi salva, então resetar o formulário
                  // Include the saved proposal data if available
                  const updatedValues = savedData?.id ? {
                    ...formValues,
                    id: savedData.id
                  } : formValues;

                  methods.reset(updatedValues, {
                    keepValues: true,
                    keepDirty: false,
                    keepIsSubmitted: false,
                    keepTouched: false
                  });

                  // Force the form to be marked as pristine (not dirty)
                  setTimeout(() => {
                    if (methods.formState.isDirty) {
                      console.log("Forcing form to be marked as not dirty after error");
                      methods.formState.isDirty = false;
                    }
                  }, 0);

                  return true; // Parar de tentar em caso de erro
                }
              };

              // Verificar o arquivo a cada 1 segundo até encontrá-lo ou atingir o limite de tentativas
              const checkInterval = setInterval(async () => {
                const shouldStop = await checkFile();
                if (shouldStop) {
                  clearInterval(checkInterval);
                }
              }, 1000);
            } else {
              // Se não conseguiu salvar, parar o carregamento
              setLoading(false);

              // Verificar qual é o problema específico
              if (!savedData) {
                console.error("Erro: savedData é null ou undefined");
                showError("Erro", "Não foi possível salvar a proposta. Verifique os dados e tente novamente.");
              } else {
                console.error("Erro: savedData não possui ID", savedData);
                showError("Erro", "A proposta foi salva parcialmente. Tente novamente ou entre em contato com o suporte.");
              }
            }
          }}
          disabled={isFormDisabled || loading || sendingToClient}
        >
          {loading ? "Processando..." : (proposalState?.id ? "Atualizar" : "Salvar")}
        </Button>
      </div>

      {/* Botões de enviar/reenviar para cliente em uma linha separada */}
      {(proposal?.situation === "UNDER_ANALYSIS") && onStatusChange && (
        <div className="mt-1 mb-4 px-2 sm:pb-2 pb-8">
          <SendProposalEmailWithAttachment
            proposal={proposal}
            customerId={formValues.customerId}
            fileEditorId={proposalState?.fileEditorId}
            className="bg-orange-500 hover:bg-orange-600 w-full"
            disabled={isFormDisabled || sendingToClient || loading ||
              !fileEditorIdAvailable || !proposalState?.fileEditorId
            }
            isLoading={sendingToClient}
            onSendingChange={setSendingToClient}
            onSuccess={async () => {
              if (proposal.id) {
                await onStatusChange(proposal.id, "PROPOSAL_SENT");
                showSuccess("Sucesso", "Proposta enviada com sucesso!");
                if (onCancelClick) onCancelClick();
              }
            }}
          />
        </div>
      )}

      {/* Botão de enviar contrato para propostas aceitas */}
      {proposal?.situation === "PROPOSAL_ACCEPTED" && onStatusChange && (
        <div className="mt-1 mb-4 px-2 sm:pb-2 pb-8">
          <SendProposalEmailWithAttachment
            proposal={proposal}
            customerId={formValues.customerId}
            fileEditorId={proposalState?.fileEditorId}
            className="bg-orange-500 hover:bg-orange-600 w-full"
            disabled={isFormDisabled || sendingToClient || loading || !fileEditorIdAvailable || !proposalState?.fileEditorId ||
              !formValues.proposalTemplateId || !templatesFiltered.some(t => t.id === formValues.proposalTemplateId && t.type === "CONTRACT")}
            isLoading={sendingToClient}
            isResend={false}
            onSendingChange={setSendingToClient}
            onSuccess={async () => {
              if (proposal.id) {
                const hasContractTemplate = formValues.proposalTemplateId &&
                  templatesFiltered.some(t =>
                    t.id === formValues.proposalTemplateId &&
                    t.type === "CONTRACT");
                if (hasContractTemplate) {
                  // Salvar a proposta com o template de contrato selecionado
                  const proposalData = {
                    ...formValues,
                    id: proposal.id,
                    plannings: planningFormRef.current?.planningForms || [],
                    workTotalCost: 0
                  };
                  const savedData = await saveData(proposalData, false, false);
                  if (savedData && savedData.id) {
                    await onStatusChange(proposal.id, "SIGN_REQUESTED");
                    showSuccess("Sucesso", "Contrato enviado ao cliente!");
                    if (onCancelClick) onCancelClick();
                  } else {
                    showError("Erro", "Não foi possível salvar o contrato. Tente novamente.");
                  }
                } else {
                  showError("Atenção", "Selecione um template de contrato válido.");
                }
              }
            }}
          />
        </div>
      )}

      {/* Botão de reenviar para cliente (para propostas enviadas e aguardando assinatura) */}
      {(proposal?.situation === "PROPOSAL_SENT" || proposal?.situation === "SIGN_REQUESTED") && (
        <div className="mt-1 mb-4 px-2 sm:pb-2 pb-8">
          <SendProposalEmailWithAttachment
            proposal={proposal}
            customerId={formValues.customerId}
            fileEditorId={proposalState?.fileEditorId}
            className="bg-orange-500 hover:bg-orange-600 w-full"
            disabled={isFormDisabled || sendingToClient || loading || !fileEditorIdAvailable || !proposalState?.fileEditorId}
            isLoading={sendingToClient}
            isResend={true}
            onSendingChange={setSendingToClient}
            onSuccess={async () => {
              showSuccess("Sucesso", proposal?.situation === "SIGN_REQUESTED" ?
                "Contrato reenviado com sucesso!" :
                "Proposta reenviada com sucesso!");
              if (onCancelClick) onCancelClick();
            }}
          />
        </div>
      )}
      {/* Espaço extra no final para garantir que o rodapé seja visível no mobile */}
      <div className="h-20 w-full max-sm:block hidden"></div>
    </div>
  );
}
