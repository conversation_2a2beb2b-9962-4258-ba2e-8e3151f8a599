"use client";

import { useEffect, useState } from "react";
import { DocumentEditor } from "@onlyoffice/document-editor-react";

// Declarar o tipo global para o DocEditor
declare global {
  interface Window {
    DocEditor?: any;
  }
}

const DOCUMENT_SERVER_URL = process.env.NEXT_PUBLIC_ONLYOFFICE_API_URL;
const DOCUMENT_CALLBACK_URL = process.env.NEXT_PUBLIC_APP_URL;

type OnlyofficeRenderProps = {
  fileKey: string;
  documentType: "word" | "cell" | "slide";
  title: string;
  mimetype: string;
  editorRef?: React.MutableRefObject<any>;
};

export default function OnlyofficeRender({
  fileKey,
  documentType,
  title,
  mimetype,
  editorRef
}: OnlyofficeRenderProps) {
  const [documentConfig, setDocumentConfig] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDocument = async () => {
      try {
        // Passo 1: Obter URL versionada
        const urlResponse = await fetch(`/api/generate-url?key=${fileKey}`);
        if (!urlResponse.ok) throw new Error('Falha ao buscar URL do documento');

        const { url } = await urlResponse.json();

        // Passo 2: Configurar parâmetros do editor
        const newConfig = {
          document: {
            fileType: getFileExtension(mimetype),
            key: `${fileKey}_${Date.now()}`, // Chave única por sessão
            title,
            url,
            permissions: {
              edit: true,
              print: true,
              download: true,
              review: true,
              comment: true
            }
          },
          documentType,
          editorConfig: {
            callbackUrl: `${DOCUMENT_CALLBACK_URL}/api/onlyoffice/callback`,
            mode: "edit",
            lang: "pt-BR",
            customization: {
              autosave: true,
              forcesave: true
            },
            user: {
              id: "1",
              name: "user"
            }
          }
        };

        setDocumentConfig(newConfig);
        setError(null);

      } catch (error) {
        console.error('Erro ao carregar documento:', error);
        setError('Erro ao carregar o editor. Tente recarregar a página.');
      }
    };

    loadDocument();
  }, [fileKey, mimetype, documentType, title]);

  const getFileExtension = (mime: string) => {
    const extensions: { [key: string]: string } = {
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
      "application/pdf": "pdf"
    };
    return extensions[mime] || "docx";
  };

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-red-500">
        {error}
      </div>
    );
  }

  // Função para lidar com eventos do editor
  const handleEvents = (event: any) => {
    // Quando o editor estiver pronto, armazenar a referência
    if (event.type === 'ready' && editorRef) {
      const api = event.target;
      editorRef.current = api;
    }
  };

  return (
    <div className="h-full w-full">
      {documentConfig ? (
        <DocumentEditor
          id="onlyoffice-editor"
          documentServerUrl={DOCUMENT_SERVER_URL || ""}
          config={documentConfig}
          events_onDocumentReady={() => {
            console.log("Documento pronto");
            // Se tiver referência, armazenar o editor
            if (editorRef && window.DocEditor) {
              // Armazenar a referência ao editor
              editorRef.current = window.DocEditor;
            }
          }}
          events_onAppReady={handleEvents}
          onLoadComponentError={(errorCode, errorDesc) => {
            console.error(`Erro no editor: ${errorCode} - ${errorDesc}`);
            setError("Falha ao inicializar o editor de documentos");
          }}
        />
      ) : (
        <div className="h-full w-full flex items-center justify-center">
          Inicializando editor...
        </div>
      )}
    </div>
  );
}