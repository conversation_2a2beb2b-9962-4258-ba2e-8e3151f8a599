"use server";
import { prisma } from "@/src/lib/prisma";
import { Contact } from "@/src/types/core/contact";
import { ContactSchema } from "@/src/app/views/crm/customers/_schemas/contact.schema";

export async function loadContacts(customerId: string) {
	try {
		const data: Contact[] = await prisma.contact.findMany({
			where: { customerId },
		});
		return data;
	} catch (error) {
		console.error(error);
	}
}

export async function saveContact(contact: ContactSchema) {
	try {
		let data: Contact;
		if (contact.id) {
			data = await prisma.contact.update({
				where: { id: contact.id },
				data: contact,
			});
		} else {
			delete contact.id;
			data = await prisma.contact.create({
				data: {
					...contact,
					customerId: contact.customerId!,
				},
			});
		}
		return data;
	} catch (error) {
		console.error(error);
	}
}

export async function removeContact(id: string) {
	try {
		await prisma.contact.delete({ where: { id } });
		return { message: "Contato removido com sucesso" };
	} catch (error) {
		console.error(error);
	}
}
